from flask import Flask, jsonify, redirect, request

app = Flask(__name__)

ERROR_MESSAGE = {"statusText": "服务已迁移至新端口 https://apiv2.aliyahzombie.top"}
ERROR_STATUS_CODE = 301

@app.route('/', defaults={'path': ''}, methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'])
@app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'])
def catch_all(path):
    """
    Catches all requests and returns a fixed error message.
    """
    return jsonify(ERROR_MESSAGE), ERROR_STATUS_CODE


@app.errorhandler(404)
def not_found(error):
    """
    Handles 404 errors by returning the fixed error message.
    """
    return jsonify(ERROR_MESSAGE), ERROR_STATUS_CODE

@app.errorhandler(405)
def method_not_allowed(error):
    """
    Handles 405 errors by returning the fixed error message.
    """
    return jsonify(ERROR_MESSAGE), ERROR_STATUS_CODE


if __name__ == '__main__':
    # For production, use a proper WSGI server like gunicorn or uWSGI.
    #  This development server is for testing only.
    app.run(host='0.0.0.0', port=18123)  # Change port if needed.  Also important to listen on 0.0.0.0 for external access
