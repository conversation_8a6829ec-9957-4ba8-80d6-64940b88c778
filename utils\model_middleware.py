# -*- coding: utf-8 -*-
"""
模型覆写中间件
支持通过模型名称后缀来启用特殊功能
"""
import re
from utils.logger import log_info, log_debug, log_warning


class ModelMiddleware:
    """模型覆写中间件"""
    
    def __init__(self):
        self.processors = {}
        self._register_default_processors()
    
    def _register_default_processors(self):
        """注册默认的处理器"""
        self.register_processor('search', self._process_search)
        self.register_processor('vision', self._process_vision)
        self.register_processor('fast', self._process_fast)
        self.register_processor('pro', self._process_pro)
    
    def register_processor(self, suffix, processor_func):
        """注册新的处理器
        
        Args:
            suffix: 模型名称后缀（如 'search', 'vision'）
            processor_func: 处理函数，接收 (model_name, payload) 参数
        """
        self.processors[suffix] = processor_func
        log_debug(f"注册模型处理器: {suffix}")
    
    def process_request(self, model_name, payload):
        """处理请求，应用模型覆写
        
        Args:
            model_name: 原始模型名称
            payload: 请求载荷
            
        Returns:
            tuple: (new_model_name, modified_payload)
        """
        # 解析模型名称
        base_model, suffixes = self._parse_model_name(model_name)
        
        if not suffixes:
            # 没有后缀，直接返回
            return model_name, payload
        
        log_info(f"检测到模型覆写: {model_name} -> 基础模型: {base_model}, 后缀: {suffixes}")
        
        # 复制payload以避免修改原始数据
        modified_payload = payload.copy() if payload else {}
        
        # 按顺序应用所有处理器
        for suffix in suffixes:
            if suffix in self.processors:
                try:
                    modified_payload = self.processors[suffix](base_model, modified_payload)
                    log_debug(f"应用处理器 '{suffix}' 成功")
                except Exception as e:
                    log_warning(f"处理器 '{suffix}' 执行失败: {e}")
            else:
                log_warning(f"未找到处理器: {suffix}")
        
        return base_model, modified_payload
    
    def _parse_model_name(self, model_name):
        """解析模型名称，提取基础模型和后缀

        Args:
            model_name: 完整模型名称，如 'gemini-2.5-pro#search#vision'

        Returns:
            tuple: (base_model, suffixes_list)
        """
        # 检查是否包含 # 分隔符
        if '#' not in model_name:
            # 没有后缀，直接返回原模型名
            return model_name, []

        # 按 # 分割模型名称和后缀
        parts = model_name.split('#')
        base_model = parts[0]  # 第一部分是基础模型名
        suffixes = parts[1:] if len(parts) > 1 else []  # 其余部分是后缀

        return base_model, suffixes
    
    def _process_search(self, base_model, payload):
        """处理搜索功能"""
        log_info("启用Google搜索功能")
        
        # 添加Google搜索工具
        if 'tools' not in payload:
            payload['tools'] = []
        
        # 检查是否已经有搜索工具
        has_search = any(
            'google_search' in tool or 'googleSearch' in tool 
            for tool in payload['tools']
        )
        
        if not has_search:
            payload['tools'].append({
                'google_search': {}
            })
            log_debug("已添加Google搜索工具")
        
        return payload
    
    def _process_vision(self, base_model, payload):
        """处理视觉功能"""
        log_info("启用视觉分析功能")
        
        # 确保使用支持视觉的模型
        if 'flash' in base_model:
            # flash模型默认支持视觉
            pass
        elif 'pro' in base_model:
            # pro模型也支持视觉
            pass
        
        # 可以在这里添加视觉相关的参数调整
        # 例如调整图像处理参数等
        
        return payload
    
    def _process_fast(self, base_model, payload):
        """处理快速模式"""
        log_info("启用快速响应模式")
        
        # 调整生成参数以获得更快的响应
        if 'generationConfig' not in payload:
            payload['generationConfig'] = {}
        
        # 设置较低的最大输出长度以加快速度
        payload['generationConfig'].update({
            'maxOutputTokens': 1024,  # 限制输出长度
            'temperature': 0.7,       # 适中的创造性
        })
        
        return payload
    
    def _process_pro(self, base_model, payload):
        """处理专业模式"""
        log_info("启用专业分析模式")
        
        # 调整生成参数以获得更高质量的输出
        if 'generationConfig' not in payload:
            payload['generationConfig'] = {}
        
        # 设置更高质量的参数
        payload['generationConfig'].update({
            'maxOutputTokens': 8192,   # 更长的输出
            'temperature': 0.3,        # 更保守的创造性
            'topP': 0.8,              # 更集中的采样
            'topK': 40,               # 限制候选词汇
        })
        
        return payload
    
    def get_available_suffixes(self):
        """获取所有可用的后缀"""
        return list(self.processors.keys())
    
    def get_processor_info(self, suffix):
        """获取处理器信息"""
        if suffix not in self.processors:
            return None
        
        processor = self.processors[suffix]
        return {
            'name': suffix,
            'function': processor.__name__,
            'doc': processor.__doc__ or '无描述'
        }


# 全局中间件实例
_model_middleware = ModelMiddleware()


def process_model_request(model_name, payload):
    """处理模型请求的全局函数"""
    return _model_middleware.process_request(model_name, payload)


def register_model_processor(suffix, processor_func):
    """注册新的模型处理器"""
    _model_middleware.register_processor(suffix, processor_func)


def get_available_model_suffixes():
    """获取所有可用的模型后缀"""
    return _model_middleware.get_available_suffixes()


def get_model_processor_info(suffix=None):
    """获取模型处理器信息"""
    if suffix:
        return _model_middleware.get_processor_info(suffix)
    else:
        return {
            suffix: _model_middleware.get_processor_info(suffix)
            for suffix in _model_middleware.get_available_suffixes()
        }


# 示例：如何添加自定义处理器
def example_custom_processor(base_model, payload):
    """示例自定义处理器"""
    # 在这里添加自定义逻辑
    return payload


# 注册示例处理器（注释掉，仅作演示）
# register_model_processor('custom', example_custom_processor)
