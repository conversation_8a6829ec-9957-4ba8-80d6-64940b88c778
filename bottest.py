import logging
from telegram import Update
from telegram.ext import <PERSON><PERSON>uilder, CommandHandler, MessageHandler, filters, ContextTypes

# --- 配置 ---
BOT_TOKEN = "7277071542:AAHuzd-dR-_TMPppI_tT4VKnOKxcv1M39PQ"  # <--- 在这里替换成你的 Bot Token

# --- 日志设置 ---
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# --- Bot 处理函数 ---

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /start 命令，发送欢迎消息"""
    user = update.effective_user
    await update.message.reply_html(
        f"你好 {user.mention_html()}!\n\n"
        f"发送任何消息给我，我会告诉你：\n"
        f"  • 你的 Telegram User ID\n"
        f"  • 当前对话 (Chat) 的 ID\n\n"
        f"你也可以使用以下命令:\n"
        f"/getid - 获取你的 User ID\n"
        f"/getchatid - 获取当前对话 (Chat) 的 ID"
    )

async def get_id_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /getid 命令，回复用户 ID"""
    user_id = update.effective_user.id
    first_name = update.effective_user.first_name
    logger.info(f"用户 {first_name} ({user_id}) 在 chat {update.effective_chat.id} 使用 /getid 命令请求 User ID。")
    await update.message.reply_text(f"你好 {first_name}！你的 Telegram User ID 是： {user_id}")

async def get_chat_id_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /getchatid 命令，回复当前对话的 ID"""
    user_id = update.effective_user.id
    chat_id = update.effective_chat.id
    first_name = update.effective_user.first_name
    chat_type = update.effective_chat.type

    logger.info(f"用户 {first_name} ({user_id}) 在 {chat_type} ({chat_id}) 使用 /getchatid 命令请求 Chat ID。")
    await update.message.reply_text(
        f"你好 {first_name} (User ID: {user_id})!\n"
        f"当前对话 ({chat_type}) 的 Chat ID 是： {chat_id}"
    )

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理用户发送的普通消息 (私聊或群聊)"""
    user_id = update.effective_user.id
    chat_id = update.effective_chat.id
    first_name = update.effective_user.first_name
    chat_type = update.effective_chat.type
    text_received = update.message.text # 获取用户发送的文本 (可选)

    logger.info(f"收到来自 {first_name} ({user_id}) 在 {chat_type} ({chat_id}) 的消息: '{text_received}'")

    # 回复 User ID 和 Chat ID
    await update.message.reply_text(
        f"你好 {first_name}！\n"
        f"• 你的 User ID 是: {user_id}\n"
        f"• 当前对话 ({chat_type}) 的 Chat ID 是: {chat_id}"
    )

# --- 主程序 ---

if __name__ == '__main__':
    logger.info("启动 Bot...")

    if BOT_TOKEN == "YOUR_BOT_TOKEN":
        logger.error("错误：请将 'YOUR_BOT_TOKEN' 替换成你真实的 Bot Token！")
        exit(1)

    # 创建 Application 实例
    application = ApplicationBuilder().token(BOT_TOKEN).build()

    # 添加命令处理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("getid", get_id_command))
    application.add_handler(CommandHandler("getchatid", get_chat_id_command)) # 新增命令

    # 添加消息处理器 (处理所有非命令的文本消息)
    # filters.TEXT: 只处理文本消息
    # ~filters.COMMAND: 排除命令 (如 /start, /getid, /getchatid)
    # 注意：这次没有加 filters.ChatType.PRIVATE，所以会在所有类型的对话中响应
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    # 启动 Bot (使用轮询方式)
    logger.info("Bot 已启动，开始接收消息...")
    application.run_polling()

    logger.info("Bot 已停止。")
