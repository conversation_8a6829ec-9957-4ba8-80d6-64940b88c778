# -*- coding: utf-8 -*-
"""
应用配置管理
支持环境变量和默认配置
"""
import os
from utils.config import *


class Config:
    """基础配置类"""
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 服务器配置
    HOST = os.environ.get('HOST') or '0.0.0.0'
    PORT = int(os.environ.get('PORT') or 5000)
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 数据库配置
    DATABASE_PATH = os.environ.get('DATABASE_PATH') or DATABASE
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOGGING_ENABLED = os.environ.get('LOGGING_ENABLED', 'True').lower() == 'true'
    
    # API配置
    GEMINI_API_BASE_URL = os.environ.get('GEMINI_API_BASE_URL') or GEMINI_API_BASE
    
    # 速率限制配置
    DEFAULT_RATE_LIMIT_PER_MINUTE = int(os.environ.get('DEFAULT_RATE_LIMIT') or DEFAULT_RATE_LIMIT)
    CONTRIB_RATE_BONUS_PER_KEY = int(os.environ.get('CONTRIB_RATE_BONUS') or CONTRIB_RATE_BONUS)
    
    # 每日请求限制
    PRO_MODEL_DAILY_LIMIT = int(os.environ.get('RPD_PRO') or RPD_PRO)
    OTHER_MODEL_DAILY_LIMIT = int(os.environ.get('RPD_OTHER') or RPD_OTHER)
    CONTRIB_PRO_BONUS = int(os.environ.get('CONTRIB_PRO_LIMIT') or CONTRIB_PRO_LIMIT)
    CONTRIB_OTHER_BONUS = int(os.environ.get('CONTRB_OTHER_LIMIT') or CONTRB_OTHER_LIMIT)


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'


class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DATABASE_PATH = ':memory:'  # 使用内存数据库进行测试


# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}


def get_config():
    """获取当前环境的配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])
