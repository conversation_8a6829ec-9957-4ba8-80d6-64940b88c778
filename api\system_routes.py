# -*- coding: utf-8 -*-
from flask import Blueprint, jsonify
from database import query_db
from services import get_total_quota, get_total_usage_today, gemini_key_pool
from utils.logger import log_debug

system_bp = Blueprint('system', __name__, url_prefix='/api')


@system_bp.route('/usage', methods=['GET'])
def usage_stats():
    """获取全局使用情况统计信息。"""
    log_debug("收到获取全局使用情况统计的请求 [/api/usage]")
    
    total_quota = get_total_quota()
    total_usage = get_total_usage_today()
    total_usage_alltime = query_db(
        'SELECT COUNT(*) as count FROM usage_logs', [], one=True
    )['count'] if query_db(
        'SELECT COUNT(*) as count FROM usage_logs', [], one=True
    ) else 0
    
    response_data = {
        "usage": total_usage,
        "keys": len(gemini_key_pool),  # 当前有效的密钥数量
        "total_usage": total_usage_alltime,  # 总使用次数
    }
    log_debug(f"返回全局使用情况统计: {response_data}")
    return jsonify(response_data)
