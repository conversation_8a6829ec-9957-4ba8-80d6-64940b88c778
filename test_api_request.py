# -*- coding: utf-8 -*-
"""
测试API请求和模型覆写功能
"""
import requests
import json


def test_model_override():
    """测试模型覆写功能"""
    base_url = "http://localhost:5000"
    
    # 这里需要一个有效的API密钥，你需要替换为实际的密钥
    api_key = "test_key_123"  # 请替换为实际的API密钥
    
    # 测试数据
    test_cases = [
        {
            "name": "基础模型",
            "model": "gemini-2.5-pro",
            "expected_tools": False
        },
        {
            "name": "搜索功能",
            "model": "gemini-2.5-pro-search",
            "expected_tools": True
        },
        {
            "name": "快速模式",
            "model": "gemini-2.5-flash-fast",
            "expected_tools": False
        },
        {
            "name": "搜索+快速模式",
            "model": "gemini-2.5-pro-search-fast",
            "expected_tools": True
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== 测试: {test_case['name']} ===")
        print(f"模型: {test_case['model']}")
        
        # 构造请求
        url = f"{base_url}/v1beta/models/{test_case['model']}:generateContent"
        
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": "Hello, how are you?"
                        }
                    ]
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Goog-Api-Key": api_key
        }
        
        try:
            # 发送请求（这里只是为了测试中间件处理，实际可能会因为API密钥无效而失败）
            response = requests.post(url, json=payload, headers=headers, timeout=5)
            
            print(f"状态码: {response.status_code}")
            if response.status_code == 401:
                print("✓ 预期的认证失败（API密钥无效）")
            elif response.status_code == 503:
                print("✓ 预期的服务不可用（无可用密钥）")
            else:
                print(f"响应: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")


def test_admin_panel():
    """测试管理后台功能"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试管理后台 ===")
    
    # 测试登录页面
    try:
        response = requests.get(f"{base_url}/admin")
        print(f"管理后台访问状态码: {response.status_code}")
        if "登录" in response.text:
            print("✓ 成功重定向到登录页面")
        else:
            print("✗ 未正确重定向到登录页面")
    except Exception as e:
        print(f"访问管理后台失败: {e}")


if __name__ == "__main__":
    print("开始测试API功能...")
    
    # 测试管理后台
    test_admin_panel()
    
    # 测试模型覆写（需要有效的API密钥）
    print("\n注意: 模型覆写测试需要有效的API密钥")
    print("请在test_api_request.py中替换api_key变量为实际的密钥")
    
    # 如果你有有效的API密钥，可以取消注释下面这行
    # test_model_override()
    
    print("\n✅ 测试完成!")
