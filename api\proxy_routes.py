# -*- coding: utf-8 -*-
import uuid
import time
import re
import requests
from flask import Blueprint, request, jsonify, Response, redirect, current_app
from services import (
    get_user_by_api_key, check_rate_limit, get_available_gemini_key,
    key_reach_limit, set_key_invalid, log_usage
)
from utils.config import GEMINI_API_BASE, DEFAULT_SAFETY_SETTINGS
from utils.logger import log_debug, log_warning, log_info, log_error

proxy_bp = Blueprint('proxy', __name__)


@proxy_bp.route('/')
def index():
    """根路径重定向到指定的 URL。"""
    log_info("收到根路径请求，重定向到 https://geminipool.aliyahzombie.top/")
    return redirect("https://geminipool.aliyahzombie.top/", code=302)


@proxy_bp.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def proxy_all(path):
    """代理所有到 Gemini API 的请求，并进行用户认证、速率限制和密钥轮换。"""
    request_id = str(uuid.uuid4())[:8]
    log_debug(f"[Req ID: {request_id}] 收到代理请求: {request.method} /{path} | 来源 IP: {request.remote_addr}")

    # 1. 提取用户 API 密钥
    user_key = request.args.get('key')
    if not user_key:
        user_key = request.headers.get('X-Goog-Api-Key')
        if user_key:
             log_debug(f"[Req ID: {request_id}] 从 X-Goog-Api-Key Header 中获取用户密钥")
        else:
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                user_key = auth_header[7:]
    
    query_params = request.args.to_dict()
    if 'key' in query_params:
        del query_params['key']

    log_debug(f"[Req ID: {request_id}] 原始请求参数: {request.args}")
    log_debug(f"[Req ID: {request_id}] 原始请求头: {dict(request.headers)}")

    if not user_key:
        log_warning(f"[Req ID: {request_id}] 请求未提供 API 密钥。")
        return jsonify({"error": "API key required"}), 401
    
    log_debug(f"[Req ID: {request_id}] 使用用户密钥 key=***{user_key[-4:]} 进行验证")

    # 2. 用户验证和速率限制检查（仅对生成请求）
    if ("generate" in path.lower() and "v1beta/models/" in path) or "chat/completions" in path:
        log_debug(f"[Req ID: {request_id}] 检测到生成请求，进行身份验证")
        user = get_user_by_api_key(user_key)
        if not user:
            return jsonify({"error": "Invalid API key"}), 401
        
        log_debug(f"[Req ID: {request_id}] 用户验证成功: ID={user['id']}, Username={user['username']}")

        # 3. 速率限制检查
        if "generate" in path.lower() and "v1beta/models/" in path:
            modelName = re.findall(r"v1beta/models/(.*?):", path)[0]
            if not check_rate_limit(user['id'], modelName):
                return jsonify({"error": "Rate limit exceeded"}), 429
            
        log_debug(f"[Req ID: {request_id}] 用户 ID={user['id']} 速率限制检查通过。")

    # 定义一个函数来获取可用的 Gemini Key 并处理 429 错误
    def get_gemini_key_with_retry(request_id):
        """获取可用的Gemini密钥"""
        google_api_key = get_available_gemini_key()
        if not google_api_key:
            log_error(f"[Req ID: {request_id}] 无法获取可用的 Gemini API 密钥，密钥池为空或所有密钥均无效。")
            return None, None

        # 构造目标 Google API URL
        google_url = f"{GEMINI_API_BASE}/{path}"
        # 将我们选的 Google API 密钥添加到参数中
        query_params['key'] = google_api_key
        # 重新构建查询字符串
        query_string = '&'.join(f"{k}={v}" for k, v in query_params.items())
        google_url += f"?{query_string}"

        log_debug(f"[Req ID: {request_id}] 构造目标 Gemini URL: {google_url.replace(google_api_key, '***' + google_api_key[-4:])}")
        return google_api_key, google_url
    
    # 兼容oneapi
    if path == " v1/models":
        path = "v1beta/models"

    # 获取可用的 Gemini 密钥
    google_api_key, google_url = get_gemini_key_with_retry(request_id)
    if not google_api_key:
        return jsonify({"error": "No available Gemini API keys in the pool"}), 503

    # 6. 获取请求体并应用安全设置
    payload = request.get_json(silent=True)
    log_info(f"[Req ID: {request_id}] 请求体 (Payload): {payload}")

    # 应用默认安全设置
    if request.method == 'POST' and payload:
        if "contents" in payload and "safetySettings" not in payload:
            payload = {**payload, **DEFAULT_SAFETY_SETTINGS}
            log_debug(f"[Req ID: {request_id}] 应用默认安全设置。")
            log_debug(f"[Req ID: {request_id}] 应用安全设置后的 Payload: {payload}")

    # 7. 复制必要的请求头
    headers = {k: v for k, v in request.headers.items()
              if k.lower() in ['content-type', 'authorization', 'user-agent']}
    
    # Auto adapt OpenAI endpoint / Gemini endpoint
    if "openai" in path:
        headers["Authorization"] = f"Bearer {google_api_key}"
        headers.pop("X-Goog-Api-Key", None)
    else:
        headers["X-Goog-Api-Key"] = google_api_key
        headers.pop("Authorization", None)
    
    if payload and 'content-type' not in (k.lower() for k in headers):
        headers["Content-Type"] = "application/json"
        log_debug(f"[Req ID: {request_id}] 添加 'Content-Type: application/json'")
    
    log_debug(f"[Req ID: {request_id}] 转发给 Gemini 的请求头: {headers}")

    # 8. 判断是否为流式请求
    is_streaming = "streamGenerateContent" in path and request.args.get('alt') == 'sse'
    if not is_streaming:
        is_streaming = payload.get('stream', False) if payload else is_streaming
    
    if is_streaming:
        log_debug(f"[Req ID: {request_id}] 检测到流式请求 (SSE)。")
    else:
        log_debug(f"[Req ID: {request_id}] 检测到非流式请求。")

    # 定义一个变量来标记是否需要记录使用情况
    should_log_usage = False

    # 9. 记录使用情况 - 提前记录以保证计数准确
    if 'generatecontent' in str(path).lower() or "chat/completions" in str(path).lower():
        should_log_usage = True

    try:
        if is_streaming:
            # 处理流式响应
            response_generator = stream_response_with_retry(
                request.method, google_url, payload, headers, request_id, google_api_key
            )

            response = Response(
                response_generator,
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'X-Accel-Buffering': 'no'
                }
            )
            log_info(f"[Req ID: {request_id}] 已创建流式响应对象，准备将数据流返回给客户端。")

            # 仅在成功创建响应对象后记录使用情况
            if should_log_usage and 'user' in locals():
                log_usage(user['id'], path, 200, is_streaming)
                log_info(f"[Req ID: {request_id}] 已记录用户 ID={user['id']} 的使用情况 (流式)。")

            return response

        else:
            # 处理非流式响应
            log_info(f"[Req ID: {request_id}] 开始向上游 Gemini 发送非流式请求...")
            start_time = time.time()

            from utils.resource_manager import safe_http_request
            with safe_http_request(
                method=request.method,
                url=google_url,
                json=payload if payload else None,
                headers=headers,
                timeout=600
            ) as api_response:
                duration = time.time() - start_time
                log_info(f"[Req ID: {request_id}] 收到来自 Gemini 的非流式响应: Status={api_response.status_code}, Duration={duration:.2f}s")

                if api_response.status_code != 200:
                     log_warning(f"[Req ID: {request_id}] Gemini API 返回非 200 状态码: {api_response.status_code}")
                     log_debug(f"[Req ID: {request_id}] Gemini 错误响应内容: {api_response.text[:500]}")

                     if api_response.status_code in [400, 403] and "API key not valid" in api_response.text:
                         log_warning(f"[Req ID: {request_id}] 检测到 Gemini API 密钥 key=***{google_api_key[-4:]} 可能无效，将标记为无效。")
                         with current_app.app_context():
                             set_key_invalid(google_api_key)
                     elif api_response.status_code == 401 and "UNAUTHENTICATED" in api_response.text:
                         log_warning(f"[Req ID: {request_id}] 检测到 Gemini API 密钥 key=***{google_api_key[-4:]} 可能无效，将标记为无效。")
                         with current_app.app_context():
                             set_key_invalid(google_api_key)
                     elif api_response.status_code == 429:
                         key_reach_limit(google_api_key)
                         log_warning(f"[Req ID: {request_id}] Gemini API 密钥 key=***{google_api_key[-4:]} 达到了速率限制。")
                         # 这里可以实现重试逻辑，但为了简化，暂时直接返回错误

                api_response.raise_for_status()

                response = Response(
                    api_response.content,
                    status=api_response.status_code,
                    mimetype=api_response.headers.get('Content-Type', 'application/json')
                )
                log_info(f"[Req ID: {request_id}] 已创建非流式响应对象，准备将数据返回给客户端。")

                # 仅在成功生成响应后记录使用情况
                if should_log_usage and 'user' in locals():
                    log_usage(user['id'], path, 200, is_streaming)
                    log_info(f"[Req ID: {request_id}] 已记录用户 ID={user['id']} 的使用情况 (非流式)。")

        return response
        
    except requests.exceptions.RequestException as e:
        error_code = 500
        error_message = f"连接 Gemini API 时出错: {str(e)}"
        
        if e.response is not None:
            error_code = e.response.status_code
            error_message = f"Gemini API 请求失败: Status={e.response.status_code}, Response={e.response.text[:500]}"
            log_error(f"[Req ID: {request_id}] {error_message}")
            if error_code in [400, 403] and "API key not valid" in e.response.text:
                 log_warning(f"[Req ID: {request_id}] 检测到 Gemini API 密钥 key=***{google_api_key[-4:]} 可能无效（通过异常），将标记为无效。")
                 with current_app.app_context():
                     set_key_invalid(google_api_key)
            elif error_code == 429:
                 log_warning(f"[Req ID: {request_id}] Gemini API 密钥 key=***{google_api_key[-4:]} 达到了速率限制（通过异常）。")
        else:
            log_error(f"[Req ID: {request_id}] {error_message}")
        
        return jsonify({"error": error_message}), error_code
    except Exception as e:
        log_error(f"[Req ID: {request_id}] 处理代理请求时发生意外错误: {e}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500


def stream_response_with_retry(method, google_url, payload, headers, req_id, initial_google_api_key):
    """生成器函数，用于从 Google API 流式传输 SSE 响应，包含重试机制。"""
    log_debug(f"[Req ID: {req_id}] 进入流式响应生成器 stream_response_with_retry...")
    max_retries = 5
    google_api_key = initial_google_api_key
    current_google_url = google_url

    def get_gemini_key_with_retry_inner(req_id):
        """内部函数：获取可用的Gemini密钥"""
        google_api_key = get_available_gemini_key()
        if not google_api_key:
            log_error(f"[Req ID: {req_id}] 无法获取可用的 Gemini API 密钥，密钥池为空或所有密钥均无效。")
            return None, None

        # 重新构造URL
        base_url = google_url.split('?')[0]
        query_params = {}
        if '?' in google_url:
            query_string = google_url.split('?')[1]
            for param in query_string.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    query_params[key] = value

        query_params['key'] = google_api_key
        new_query_string = '&'.join(f"{k}={v}" for k, v in query_params.items())
        new_google_url = f"{base_url}?{new_query_string}"

        return google_api_key, new_google_url

    for attempt in range(max_retries):
        try:
            from utils.resource_manager import safe_streaming_request
            with safe_streaming_request(
                method=method,
                url=current_google_url,
                json=payload if payload else None,
                headers=headers,
                timeout=600
            ) as response:
                log_info(f"[Req ID: {req_id}] 已连接到 Gemini 流式端点，状态码: {response.status_code} (尝试 {attempt + 1}/{max_retries})")

                if response.status_code != 200:
                    log_warning(f"[Req ID: {req_id}] Gemini API 流式端点返回非 200 状态码: {response.status_code}")
                    error_text = response.text[:500]
                    log_debug(f"[Req ID: {req_id}] Gemini 流式错误响应内容片段: {error_text}")

                    if response.status_code in [400, 403] and "API key not valid" in error_text:
                        log_warning(f"[Req ID: {req_id}] 检测到 Gemini API 密钥 key=***{google_api_key[-4:]} 可能无效（流式），将标记为无效。")
                        with current_app.app_context():
                            set_key_invalid(google_api_key)
                        raise requests.exceptions.RequestException(f"Invalid API key", response=response)
                    elif response.status_code == 401 and "UNAUTHENTICATED" in response.text:
                        log_warning(f"[Req ID: {req_id}] 检测到 Gemini API 密钥 key=***{google_api_key[-4:]} 可能无效，将标记为无效。")
                        with current_app.app_context():
                            set_key_invalid(google_api_key)
                        raise requests.exceptions.RequestException("Invalid API key", response=response)
                    elif response.status_code == 429:
                        log_warning(f"[Req ID: {req_id}] Gemini API 密钥 key=***{google_api_key[-4:]} 达到了速率限制（流式）。")
                        key_reach_limit(google_api_key)
                        # 尝试获取新的密钥和 URL
                        new_google_api_key, new_google_url = get_gemini_key_with_retry_inner(req_id)
                        if new_google_api_key:
                            log_info(f"[Req ID: {req_id}] 获取到新的 Gemini API 密钥，准备重试。")
                            google_api_key = new_google_api_key
                            current_google_url = new_google_url
                            continue
                        else:
                            log_error(f"[Req ID: {req_id}] 无法获取新的 Gemini API 密钥，放弃重试。")
                            raise requests.exceptions.RequestException("No available API keys after retries", response=response)

                response.raise_for_status()

                log_info(f"[Req ID: {req_id}] 开始从 Gemini 流式传输数据块...")
                chunk_count = 0
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        chunk_count += 1
                        log_debug(f"[Req ID: {req_id}] 转发数据块 #{chunk_count} (大小: {len(chunk)} bytes)")
                        yield chunk

                log_info(f"[Req ID: {req_id}] Gemini 数据流传输完成。共转发 {chunk_count} 个数据块。")
                break  # 成功完成，跳出重试循环

        except requests.exceptions.RequestException as e:
            error_message = f"流式连接 Gemini API 时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
            log_error(f"[Req ID: {req_id}] {error_message}")

            if attempt < max_retries - 1:
                log_info(f"[Req ID: {req_id}] 准备重试流式请求 (尝试 {attempt + 2}/{max_retries})...")
            else:
                 log_error(f"[Req ID: {req_id}] 达到最大重试次数，流式传输失败。")
                 break
        except Exception as e:
            log_error(f"[Req ID: {req_id}] 流式响应生成器发生意外错误: {e}", exc_info=True)
            log_error(f"[Req ID: {req_id}] 流式传输因意外错误而中止。")
            break
    else:
        log_error(f"[Req ID: {req_id}] 达到最大重试次数，流式传输失败。")
