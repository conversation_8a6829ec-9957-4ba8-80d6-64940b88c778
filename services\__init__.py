# -*- coding: utf-8 -*-
"""
业务服务模块
包含用户管理、密钥管理、速率限制等业务逻辑
"""

from .user_service import (
    get_user_by_api_key, get_user_by_linuxdo_id, create_or_update_user,
    regenerate_api_key, log_usage, get_user_usage_today, get_total_usage_today
)
from .key_service import (
    validate_gemini_key, add_gemini_key, update_gemini_keys, set_key_invalid,
    get_available_gemini_key, key_reach_limit, invalidate_keys_with_whitespace,
    check_all_keys, get_total_quota, gemini_key_pool, rate_limited_reached_pool
)
from .rate_limit_service import (
    get_user_rate_limit, check_rate_limit, get_user_daily_usage_by_model
)

__all__ = [
    # User service
    'get_user_by_api_key', 'get_user_by_linuxdo_id', 'create_or_update_user',
    'regenerate_api_key', 'log_usage', 'get_user_usage_today', 'get_total_usage_today',
    
    # Key service
    'validate_gemini_key', 'add_gemini_key', 'update_gemini_keys', 'set_key_invalid',
    'get_available_gemini_key', 'key_reach_limit', 'invalidate_keys_with_whitespace',
    'check_all_keys', 'get_total_quota', 'gemini_key_pool', 'rate_limited_reached_pool',
    
    # Rate limit service
    'get_user_rate_limit', 'check_rate_limit', 'get_user_daily_usage_by_model'
]
