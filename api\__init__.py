# -*- coding: utf-8 -*-
"""
API路由模块
包含所有API端点的路由定义
"""

from .admin_routes import admin_bp
from .admin_panel import admin_panel_bp
from .user_routes import user_bp
from .system_routes import system_bp
from .proxy_routes import proxy_bp
from .health_routes import health_bp
from .error_handlers import register_error_handlers

__all__ = ['admin_bp', 'admin_panel_bp', 'user_bp', 'system_bp', 'proxy_bp', 'health_bp', 'register_error_handlers']


def register_blueprints(app):
    """注册所有蓝图到Flask应用"""
    app.register_blueprint(admin_bp)
    app.register_blueprint(admin_panel_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(system_bp)
    app.register_blueprint(proxy_bp)
    app.register_blueprint(health_bp)
    register_error_handlers(app)
