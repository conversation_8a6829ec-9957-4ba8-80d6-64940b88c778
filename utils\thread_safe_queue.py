# -*- coding: utf-8 -*-
import threading
import collections.abc


class QueueEmpty(Exception):
    "Exception raised when trying to get from an empty ThreadSafeListQueue."
    pass


class QueueFull(Exception):
    "Exception raised when trying to put into a full ThreadSafeListQueue."
    pass


class ThreadSafeListQueue(collections.abc.Sequence):
    """
    一个线程安全的列表式队列。

    它内部使用一个 list 来存储元素，并使用 threading.Lock 来保护所有操作，
    确保在多线程环境下的安全访问。

    提供了 put() 和 get() 方法实现 FIFO 队列功能（get 默认阻塞）。
    同时，它也实现了 collections.abc.Sequence 的部分接口（如 __len__,
    __getitem__）以及其他常用列表方法（如 append, insert, pop, remove等），
    所有这些方法都是线程安全的。

    注意：
    - 迭代 (`for item in queue`) 是基于获取锁时的列表快照，
      迭代过程中其他线程对列表的修改不会反映在当前迭代中。
    - 性能开销：每次操作都需要获取和释放锁，在高并发下可能有性能影响。
    """

    def __init__(self, maxsize=0):
        """
        初始化线程安全的列表队列。

        Args:
            maxsize (int): 队列的最大容量。0 表示无限制。
        """
        self._list = []
        # 使用 RLock 允许同一线程在持有锁时再次获取它（例如方法调用方法）
        # 对于这个实现，Lock 也能工作，但 RLock 更灵活些
        self._lock = threading.RLock()
        self._maxsize = int(maxsize) if maxsize > 0 else 0

        # Condition variable for blocking get() and put()
        # 它使用我们上面创建的锁
        self._condition = threading.Condition(self._lock)

    # --- Queue Interface ---

    def put(self, item, block=True, timeout=None):
        """
        将元素放入队列尾部（入队）。

        Args:
            item: 要放入队列的元素。
            block (bool): 如果队列已满且 block=True，则阻塞等待直到有空间。
                          如果 block=False 且队列已满，则引发 QueueFull 异常。
            timeout (float, optional): 最长阻塞等待时间（秒）。如果超时仍无法放入，
                                       且 block=True，则引发 QueueFull 异常。

        Raises:
            QueueFull: 如果队列已满且无法在指定条件下放入元素。
        """
        with self._condition: # 获取锁 (Condition 会管理锁)
            if self._maxsize > 0:
                if not block:
                    if len(self._list) >= self._maxsize:
                        raise QueueFull
                else:
                    # 等待条件：队列未满 (len < maxsize)
                    if not self._condition.wait_for(lambda: len(self._list) < self._maxsize, timeout):
                        # 等待超时或中断，仍然满
                        raise QueueFull
            # 到这里时，要么队列无限大，要么有空间了
            self._list.append(item)
            # 通知一个可能在等待 get 的线程，队列现在非空了
            self._condition.notify() # notify(1) is implicit

    def put_nowait(self, item):
        """非阻塞版本的 put。如果队列满则引发 QueueFull。"""
        self.put(item, block=False)

    def get(self, block=True, timeout=None):
        """
        从队列头部移除并返回元素（出队）。

        Args:
            block (bool): 如果队列为空且 block=True，则阻塞等待直到有元素。
                          如果 block=False 且队列为空，则引发 QueueEmpty 异常。
            timeout (float, optional): 最长阻塞等待时间（秒）。如果超时仍为空，
                                       且 block=True，则引发 QueueEmpty 异常。

        Returns:
            返回从队列头部取出的元素。

        Raises:
            QueueEmpty: 如果队列为空且无法在指定条件下获取元素。
        """
        with self._condition: # 获取锁
            if not block:
                if not self._list:
                    raise QueueEmpty
            else:
                # 等待条件：队列非空 (len > 0)
                if not self._condition.wait_for(lambda: len(self._list) > 0, timeout):
                     # 等待超时或中断，仍然空
                    raise QueueEmpty
            # 到这里时，队列肯定非空
            item = self._list.pop(0)
            # 通知一个可能在等待 put 的线程（如果队列有大小限制），队列现在有空间了
            self._condition.notify()
            return item

    def get_nowait(self):
        """非阻塞版本的 get。如果队列空则引发 QueueEmpty。"""
        return self.get(block=False)

    def empty(self):
        """检查队列是否为空 (线程安全)。"""
        with self._lock:
            return not self._list

    def full(self):
        """检查队列是否已满 (线程安全)。仅当 maxsize > 0 时有意义。"""
        with self._lock:
            return self._maxsize > 0 and len(self._list) >= self._maxsize

    def qsize(self):
        """返回队列中当前的元素数量 (线程安全)。"""
        with self._lock:
            return len(self._list)

    # --- List-like Interface (Thread-Safe) ---

    def append(self, item):
        """
        在列表末尾添加元素 (线程安全)。等同于 put(item) 但不阻塞或检查大小限制。
        如果需要阻塞或大小检查，请使用 put。
        为了与 put 行为一致（唤醒等待的 get），内部调用 put。
        """
        try:
            # 使用 put_nowait 来匹配 list.append 的非阻塞特性
            # 如果你希望 append 也能在队列满时阻塞，则调用 self.put(item)
            self.put(item, block=False)
        except QueueFull:
            # list.append 通常不会因为"满"而失败，但这里因为有 maxsize 概念
            # 可以选择默默忽略，或者重新抛出，或者抛出不同的异常
            # 这里选择重新抛出 QueueFull，明确表示容量限制生效了
            raise QueueFull("Cannot append, queue is full")

    def insert(self, index, item):
        """在指定索引处插入元素 (线程安全)。"""
        with self._condition: # 使用 condition 的锁
            # 检查是否已满 (可选，取决于你希望 insert 是否受 maxsize 影响)
            if self._maxsize > 0 and len(self._list) >= self._maxsize:
                 # 可以选择抛出 QueueFull，或者允许插入（这会违反 maxsize）
                 # 这里选择抛出 QueueFull 保持一致性
                 raise QueueFull("Cannot insert, queue is full")
            self._list.insert(index, item)
            # 因为增加了元素，通知可能等待 get 的线程
            self._condition.notify()

    def pop(self, index=-1):
        """
        移除并返回指定索引处的元素 (默认最后一个) (线程安全)。

        Args:
            index (int): 要移除元素的索引，默认为 -1 (最后一个)。

        Returns:
            被移除的元素。

        Raises:
            IndexError: 如果列表为空或索引越界。
        """
        with self._condition: # 使用 condition 的锁
            if not self._list:
                raise IndexError("pop from empty list")
            try:
                item = self._list.pop(index)
                # 因为移除了元素，通知可能等待 put 的线程
                self._condition.notify()
                return item
            except IndexError:
                # 捕获并重新引发原始的 IndexError，如果索引无效
                raise IndexError("pop index out of range")

    def remove(self, value):
        """
        移除列表中第一个出现的指定值 (线程安全)。

        Args:
            value: 要移除的值。

        Raises:
            ValueError: 如果值不在列表中。
        """
        with self._condition: # 使用 condition 的锁
            try:
                self._list.remove(value)
                 # 因为移除了元素，通知可能等待 put 的线程
                self._condition.notify()
            except ValueError:
                # 捕获并重新引发原始的 ValueError
                raise ValueError("value not found in list")

    def clear(self):
        """清空列表中的所有元素 (线程安全)。"""
        with self._condition: # 使用 condition 的锁
            self._list.clear()
            # 因为列表变空（可能之前是满的），通知可能等待 put 的线程
            self._condition.notify_all() # 通知所有等待的 put

    def __len__(self):
        """返回列表长度 (线程安全)。"""
        return self.qsize()

    def __getitem__(self, index):
        """
        获取指定索引或切片的元素 (线程安全)。
        如果是切片，返回的是列表的一个线程安全快照。
        """
        with self._lock:
            # list.__getitem__ 可以处理整数索引和切片对象
            try:
                # 对于切片，返回的是一个新的 list，自然是当时状态的快照
                # 对于单个索引，返回的是那个元素本身
                return self._list[index]
            except IndexError:
                raise IndexError("index out of range")
            except TypeError:
                raise TypeError("indices must be integers or slices")

    def __setitem__(self, index, value):
        """设置指定索引处的元素值 (线程安全)。不支持切片赋值。"""
        with self._lock:
             # 只支持整数索引赋值，切片赋值太复杂且意义不大
            if isinstance(index, slice):
                raise TypeError("ThreadSafeListQueue does not support slice assignment")
            try:
                self._list[index] = value
            except IndexError:
                raise IndexError("assignment index out of range")

    def __delitem__(self, index):
        """删除指定索引或切片的元素 (线程安全)。"""
        with self._condition: # 使用 condition 的锁
            original_len = len(self._list)
            try:
                del self._list[index]
                # 如果长度减少了，通知可能等待 put 的线程
                if len(self._list) < original_len:
                     self._condition.notify_all() # 通知所有等待 put 的
            except IndexError:
                raise IndexError("index out of range")
            except TypeError:
                raise TypeError("indices must be integers or slices")

    def __contains__(self, item):
        """检查元素是否存在于列表中 (线程安全)。"""
        with self._lock:
            return item in self._list

    def __iter__(self):
        """
        返回一个迭代器。迭代的是获取锁时列表的一个快照 (线程安全)。
        """
        with self._lock:
            # 创建当前列表的副本进行迭代，防止迭代期间列表被修改导致错误
            list_snapshot = self._list[:]
        return iter(list_snapshot)

    def __str__(self):
        """返回列表的字符串表示 (线程安全)。"""
        with self._lock:
            return f"ThreadSafeListQueue({str(self._list)}, size={len(self._list)}, maxsize={self._maxsize})"

    def __repr__(self):
        """返回对象的详细字符串表示 (线程安全)。"""
        with self._lock:
            return f"<{self.__class__.__name__} object at {hex(id(self))} containing {repr(self._list)}>"
