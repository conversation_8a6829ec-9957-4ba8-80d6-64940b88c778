# 故障排除指南

## "Too Many Open Files" 错误解决方案

### 问题描述
长时间运行后出现 "too many open files" 错误，导致所有端点无法响应。

### 根本原因
1. HTTP连接没有正确关闭
2. 数据库连接泄漏
3. 文件描述符超出系统限制

### 解决方案

#### 1. 应用层修复（已实现）
- ✅ 使用连接池管理HTTP请求
- ✅ 实现资源上下文管理器
- ✅ 自动清理数据库连接
- ✅ 添加系统监控

#### 2. 系统层配置

##### 增加文件描述符限制
```bash
# 临时增加限制
ulimit -n 65536

# 永久设置（编辑 /etc/security/limits.conf）
* soft nofile 65536
* hard nofile 65536

# 或者针对特定用户
username soft nofile 65536
username hard nofile 65536
```

##### 检查当前限制
```bash
# 查看当前限制
ulimit -n

# 查看进程使用的文件描述符
lsof -p <pid> | wc -l

# 查看系统总体情况
cat /proc/sys/fs/file-nr
```

#### 3. 监控和预警

##### 使用健康检查端点
```bash
# 检查系统状态
curl http://localhost:5000/health/detailed

# 查看详细指标
curl http://localhost:5000/health/metrics
```

##### 监控脚本示例
```bash
#!/bin/bash
# monitor.sh - 监控文件描述符使用情况

while true; do
    PID=$(pgrep -f "python.*gemini")
    if [ ! -z "$PID" ]; then
        FD_COUNT=$(lsof -p $PID 2>/dev/null | wc -l)
        LIMIT=$(ulimit -n)
        USAGE=$(echo "scale=2; $FD_COUNT * 100 / $LIMIT" | bc)
        
        echo "$(date): PID=$PID, FDs=$FD_COUNT/$LIMIT (${USAGE}%)"
        
        if (( $(echo "$USAGE > 80" | bc -l) )); then
            echo "WARNING: High file descriptor usage!"
        fi
    fi
    sleep 60
done
```

#### 4. 应急处理

##### 重启应用
```bash
# 优雅重启
pkill -TERM -f "python.*gemini"
sleep 5
python run.py

# 强制重启
pkill -KILL -f "python.*gemini"
python run.py
```

##### 清理僵尸连接
```bash
# 查找并清理僵尸连接
netstat -an | grep CLOSE_WAIT | wc -l
ss -s
```

### 预防措施

#### 1. 定期重启
设置定时任务定期重启应用：
```bash
# 添加到 crontab
0 3 * * * /path/to/restart_script.sh
```

#### 2. 监控告警
- 设置文件描述符使用率告警（>80%）
- 监控内存使用情况
- 监控网络连接数

#### 3. 负载均衡
- 使用多个应用实例
- 配置反向代理（Nginx）
- 实现请求分发

### 性能优化建议

#### 1. 连接池配置
```python
# 在 utils/resource_manager.py 中调整
pool_connections=20      # 增加连接池数量
pool_maxsize=50         # 增加每个池的最大连接数
```

#### 2. 超时设置
```python
# 调整超时时间
timeout=30              # 普通请求超时
stream_timeout=300      # 流式请求超时
```

#### 3. 清理频率
```python
# 调整清理间隔
cleanup_interval=180    # 3分钟清理一次
monitor_interval=60     # 1分钟监控一次
```

### 日志分析

#### 查找资源泄漏
```bash
# 查找HTTP请求相关错误
grep -i "connection\|timeout\|too many" /path/to/logs

# 查找数据库相关错误
grep -i "database\|sqlite\|connection" /path/to/logs

# 查找文件描述符相关信息
grep -i "file.*descriptor\|open.*file" /path/to/logs
```

#### 监控关键指标
- HTTP连接数
- 数据库连接数
- 文件描述符使用率
- 内存使用情况
- 线程数量

### 常见问题

#### Q: 为什么重启后问题暂时消失？
A: 重启会释放所有资源，但如果代码中有资源泄漏，问题会再次出现。

#### Q: 如何确定是哪种资源泄漏？
A: 使用 `lsof -p <pid>` 查看进程打开的文件，分析文件类型。

#### Q: 增加ulimit限制是否能根本解决问题？
A: 只能延缓问题出现，根本解决需要修复代码中的资源泄漏。

### 联系支持
如果问题持续存在，请提供：
1. 错误日志
2. 系统监控数据
3. 运行环境信息
4. 复现步骤
