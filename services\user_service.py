# -*- coding: utf-8 -*-
import sqlite3
import uuid
from datetime import datetime
from database import query_db, execute_db, get_db
from utils.logger import log_debug, log_warning, log_info, log_error


def get_user_by_api_key(api_key):
    """通过 API 密钥查找用户。"""
    log_debug(f"通过 API 密钥查找用户: key=***{api_key[-4:]}")
    user = query_db('SELECT * FROM users WHERE api_key = ?', [api_key], one=True)
    if user:
        log_debug(f"找到用户: ID={user['id']}, Username={user['username']}")
    else:
        log_warning(f"未找到 API 密钥对应的用户: key=***{api_key[-4:]}")
    return user


def get_user_by_linuxdo_id(linuxdo_id):
    """通过 LinuxDo ID 查找用户。"""
    log_debug(f"通过 LinuxDo ID 查找用户: ID={linuxdo_id}")
    user = query_db('SELECT * FROM users WHERE linuxdo_id = ?', [linuxdo_id], one=True)
    if user:
        log_debug(f"找到用户: ID={user['id']}, Username={user['username']}")
    else:
        log_debug(f"未找到 LinuxDo ID 对应的用户: ID={linuxdo_id}")
    return user


def create_or_update_user(linuxdo_id, username, email, avatar_url):
    """创建新用户或更新现有用户信息。"""
    log_debug(f"尝试创建或更新用户: LinuxDo ID={linuxdo_id}, Username={username}")
    user = get_user_by_linuxdo_id(linuxdo_id)
    db = get_db()
    api_key = None
    
    try:
        if user:
            # 更新现有用户
            log_debug(f"用户已存在 (ID={user['id']})，正在更新用户信息...")
            api_key = user['api_key']
            db.execute(
                'UPDATE users SET username = ?, email = ?, avatar_url = ? WHERE linuxdo_id = ?',
                [username, email, avatar_url, linuxdo_id]
            )
            log_debug(f"用户 (ID={user['id']}) 信息更新成功。")
        else:
            # 创建新用户并生成 API 密钥
            log_debug("用户不存在，正在创建新用户...")
            api_key = str(uuid.uuid4())
            log_debug(f"为新用户生成 API 密钥: key=***{api_key[-4:]}")
            db.execute(
                'INSERT INTO users (linuxdo_id, username, email, avatar_url, api_key) VALUES (?, ?, ?, ?, ?)',
                [linuxdo_id, username, email, avatar_url, api_key]
            )
            log_debug(f"新用户创建成功: LinuxDo ID={linuxdo_id}, Username={username}")
        
        db.commit()
        log_debug("用户创建/更新操作已提交。")
        return api_key
    except sqlite3.Error as e:
        log_error(f"创建或更新用户时出错 (LinuxDo ID={linuxdo_id}): {e}")
        db.rollback()
        return None


def regenerate_api_key(user_id):
    """为指定用户重新生成 API 密钥。"""
    log_info(f"正在为用户 ID={user_id} 重新生成 API 密钥...")
    new_api_key = str(uuid.uuid4())
    log_info(f"新生成的 API 密钥: key=***{new_api_key[-4:]}")
    db = get_db()
    try:
        db.execute('UPDATE users SET api_key = ? WHERE id = ?', [new_api_key, user_id])
        db.commit()
        log_info(f"用户 ID={user_id} 的 API 密钥更新成功。")
        return new_api_key
    except sqlite3.Error as e:
        log_error(f"为用户 ID={user_id} 重新生成 API 密钥时出错: {e}")
        db.rollback()
        return None


def log_usage(user_id, endpoint, status_code, is_streaming=False):
    """记录用户 API 使用情况。"""
    log_debug(f"记录用户 ID={user_id} 的使用情况: Endpoint={endpoint}, Status={status_code}, Streaming={is_streaming}")
    db = get_db()
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    try:
        db.execute(
            'INSERT INTO usage_logs (user_id, endpoint, status_code, request_time, is_streaming) VALUES (?, ?, ?, ?, ?)',
            [user_id, endpoint, status_code, current_time, 1 if is_streaming else 0]
        )
        db.commit()
        log_debug(f"用户 ID={user_id} 的使用情况已记录。")
    except sqlite3.Error as e:
        log_error(f"记录用户 ID={user_id} 使用情况时出错: {e}")
        db.rollback()


def get_user_usage_today(user_id):
    """获取用户今天的 API 使用次数。"""
    log_debug(f"查询用户 ID={user_id} 今日的使用次数...")
    # 获取今天的开始和结束时间
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999).strftime('%Y-%m-%d %H:%M:%S')
    
    usage = query_db(
        'SELECT COUNT(*) as count FROM usage_logs WHERE user_id = ? AND request_time BETWEEN ? AND ?',
        [user_id, today_start, today_end], one=True
    )
    usage_count = usage['count'] if usage else 0
    log_debug(f"用户 ID={user_id} 今日已使用 {usage_count} 次。")
    return usage_count


def get_total_usage_today():
    """获取今天所有用户的总 API 使用次数。"""
    log_debug("查询今日所有用户的总使用次数...")
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999).strftime('%Y-%m-%d %H:%M:%S')
    
    usage = query_db(
        'SELECT COUNT(*) as count FROM usage_logs WHERE request_time BETWEEN ? AND ?',
        [today_start, today_end], one=True
    )
    total_usage_count = usage['count'] if usage else 0
    log_debug(f"今日总使用次数: {total_usage_count}")
    return total_usage_count
