const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const qs = require('qs');
const cors = require('cors');

const app = express();
const port = 18124;

// OAuth2 parameters
const CLIENT_ID = 'Chei4tZDxFe9K88zAkidKgZfjDgqN87t';
const CLIENT_SECRET = 'E7Nqvckc32ynhmrR0txXMQSMklSZHy4K';
const REDIRECT_URI = 'http://*************:18124/oauth2/callback';
const AUTHORIZATION_ENDPOINT = 'https://connect.linux.do/oauth2/authorize';
const TOKEN_ENDPOINT = 'https://connect.linux.do/oauth2/token';
const USER_ENDPOINT = 'https://connect.linux.do/api/user';
const API_SERVER = 'https://api.geminipool.aliyahzombie.top';

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());

// Use session to save state and other OAuth2 related information
const session = require('express-session');
app.use(session({
    secret: crypto.randomBytes(24).toString('hex'),
    resave: false,
    saveUninitialized: true
}));

app.get('/oauth2/initiate', (req, res) => {
    req.session.oauthState = crypto.randomBytes(16).toString('hex');
    const authorizationUrl = `${AUTHORIZATION_ENDPOINT}?client_id=${CLIENT_ID}&response_type=code&redirect_uri=${REDIRECT_URI}&state=${req.session.oauthState}`;
    res.redirect(authorizationUrl);
});

app.get('/oauth2/callback', async (req, res) => {
    const { code, state } = req.query;

    if (state !== req.session.oauthState) {
        console.error('State validation failed');
        return res.status(401).send('State value does not match');
    }

    try {
        // Exchange code for token
        const data = qs.stringify({
            grant_type: 'authorization_code',
            code: code,
            redirect_uri: REDIRECT_URI
        });

        const tokenResponse = await axios.post(TOKEN_ENDPOINT, data, {
            auth: {
                username: CLIENT_ID,
                password: CLIENT_SECRET
            },
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        });

        // Get user information
        const userResponse = await axios.get(USER_ENDPOINT, {
            headers: { 'Authorization': `Bearer ${tokenResponse.data.access_token}` }
        });
        
        // Register user with our API and get an API key
        const apiResponse = await axios.post(`${API_SERVER}/api/register`, {
            linuxdo_id: userResponse.data.id,
            username: userResponse.data.username,
            email: userResponse.data.email,
            avatar_url: userResponse.data.avatar_url
        });

        // Redirect to frontend with user data
        res.redirect(`https://geminipool.aliyahzombie.top?apiKey=${apiResponse.data.api_key}&username=${userResponse.data.username}&avatar=${userResponse.data.avatar_url}`);
    } catch (error) {
        console.error('Error during token fetch or user info retrieval:', error.message);

        if (error.response) {
            console.error('Error response data:', error.response.data);
            console.error('Error response status:', error.response.status);
        } else if (error.request) {
            console.error('No response received:', error.request);
        } else {
            console.error('Error', error.message);
        }

        return res.status(500).send('Authentication failed');
    }
});

app.listen(port,'0.0.0.0', () => {
    console.log(`Auth server listening at http://localhost:${port}`);
});
