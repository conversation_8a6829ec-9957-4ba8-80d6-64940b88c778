# -*- coding: utf-8 -*-
import sqlite3
from utils.config import DATABASE
from utils.logger import log_debug, log_error, log_info


def create_tables():
    """如果表不存在，则创建所需的数据库表。"""
    log_debug(f"正在连接数据库 {DATABASE} 以检查并创建表...")
    db = None
    try:
        db = sqlite3.connect(DATABASE)  # 直接连接数据库而不使用g，因为这在应用上下文之外执行
        log_debug("连接成功。开始检查/创建表...")

        # 创建 users 表
        db.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            linuxdo_id INTEGER UNIQUE,
            username TEXT,
            email TEXT,
            avatar_url TEXT,
            api_key TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        log_debug("已检查/创建 'users' 表。")

        # 创建 api_keys 表
        db.execute('''
        CREATE TABLE IF NOT EXISTS api_keys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE,
        contributor_id INTEGER,
        is_valid BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contributor_id) REFERENCES users (id)
    )

        ''')
        log_debug("已检查/创建 'api_keys' 表。")

        # 创建 usage_logs 表
        db.execute('''
        CREATE TABLE IF NOT EXISTS usage_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        endpoint TEXT,
        status_code INTEGER,
        is_streaming INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )

        ''')
        log_debug("已检查/创建 'usage_logs' 表。")

        db.commit()
        log_debug("数据库表检查/创建完成并已提交更改。")
    except sqlite3.Error as e:
        log_error(f"创建数据库表时出错: {e}")
        if db:
            db.rollback()
    finally:
        if db:
            db.close()
            log_debug("数据库连接已关闭。")


def init_db_from_schema():
    """使用 schema.sql 文件初始化数据库。"""
    log_info("正在根据 schema.sql 初始化数据库...")
    db = None
    try:
        db = sqlite3.connect(DATABASE)
        with open('schema.sql', 'r') as f:
            db.cursor().executescript(f.read())
        db.commit()
        log_info("数据库 schema.sql 执行完毕。")
    except Exception as e:
        log_error(f"执行 schema.sql 时出错: {e}")
        if db:
            db.rollback()
    finally:
        if db:
            db.close()
