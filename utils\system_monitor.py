# -*- coding: utf-8 -*-
"""
系统监控模块
监控文件描述符使用情况和系统资源
"""
import os
import psutil
import threading
import time
from utils.logger import log_info, log_warning, log_error


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, check_interval=300):  # 5分钟检查一次
        self.check_interval = check_interval
        self.monitoring = False
        self.monitor_thread = None
        self._lock = threading.Lock()
    
    def start_monitoring(self):
        """开始监控"""
        with self._lock:
            if not self.monitoring:
                self.monitoring = True
                self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
                self.monitor_thread.start()
                log_info("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        with self._lock:
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)
                log_info("系统监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_system_resources()
                time.sleep(self.check_interval)
            except Exception as e:
                log_error(f"系统监控出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试
    
    def _check_system_resources(self):
        """检查系统资源"""
        try:
            # 检查文件描述符使用情况
            self._check_file_descriptors()
            
            # 检查内存使用情况
            self._check_memory_usage()
            
            # 检查进程状态
            self._check_process_status()
            
        except Exception as e:
            log_error(f"检查系统资源时出错: {e}")
    
    def _check_file_descriptors(self):
        """检查文件描述符使用情况"""
        try:
            process = psutil.Process()
            num_fds = process.num_fds() if hasattr(process, 'num_fds') else len(process.open_files())
            
            # 获取系统限制
            try:
                import resource
                soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
                
                # 计算使用率
                usage_rate = num_fds / soft_limit if soft_limit > 0 else 0
                
                log_info(f"文件描述符使用情况: {num_fds}/{soft_limit} ({usage_rate:.1%})")
                
                # 警告阈值
                if usage_rate > 0.8:
                    log_warning(f"文件描述符使用率过高: {usage_rate:.1%} ({num_fds}/{soft_limit})")
                    self._suggest_cleanup()
                elif usage_rate > 0.6:
                    log_warning(f"文件描述符使用率较高: {usage_rate:.1%} ({num_fds}/{soft_limit})")
                    
            except ImportError:
                log_info(f"当前打开的文件描述符数量: {num_fds}")
                
        except Exception as e:
            log_error(f"检查文件描述符时出错: {e}")
    
    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            log_info(f"内存使用情况: {memory_info.rss / 1024 / 1024:.1f}MB ({memory_percent:.1f}%)")
            
            if memory_percent > 80:
                log_warning(f"内存使用率过高: {memory_percent:.1f}%")
                
        except Exception as e:
            log_error(f"检查内存使用时出错: {e}")
    
    def _check_process_status(self):
        """检查进程状态"""
        try:
            process = psutil.Process()
            
            # 检查线程数
            num_threads = process.num_threads()
            log_info(f"当前线程数: {num_threads}")
            
            if num_threads > 100:
                log_warning(f"线程数过多: {num_threads}")
            
            # 检查连接数
            connections = process.connections()
            num_connections = len(connections)
            log_info(f"当前网络连接数: {num_connections}")
            
            if num_connections > 200:
                log_warning(f"网络连接数过多: {num_connections}")
                
        except Exception as e:
            log_error(f"检查进程状态时出错: {e}")
    
    def _suggest_cleanup(self):
        """建议清理操作"""
        log_warning("建议执行以下操作:")
        log_warning("1. 检查是否有未关闭的文件或网络连接")
        log_warning("2. 重启应用程序以释放资源")
        log_warning("3. 检查系统ulimit设置")
        
        # 触发资源清理
        try:
            from utils.resource_manager import cleanup_resources
            cleanup_resources()
            log_info("已执行资源清理")
        except Exception as e:
            log_error(f"执行资源清理时出错: {e}")
    
    def get_current_stats(self):
        """获取当前统计信息"""
        try:
            process = psutil.Process()
            
            stats = {
                'pid': process.pid,
                'memory_mb': process.memory_info().rss / 1024 / 1024,
                'memory_percent': process.memory_percent(),
                'num_threads': process.num_threads(),
                'num_connections': len(process.connections()),
            }
            
            # 文件描述符
            try:
                stats['num_fds'] = process.num_fds() if hasattr(process, 'num_fds') else len(process.open_files())
            except:
                stats['num_fds'] = 0
            
            # 系统限制
            try:
                import resource
                soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
                stats['fd_soft_limit'] = soft_limit
                stats['fd_hard_limit'] = hard_limit
                stats['fd_usage_rate'] = stats['num_fds'] / soft_limit if soft_limit > 0 else 0
            except:
                stats['fd_soft_limit'] = 0
                stats['fd_hard_limit'] = 0
                stats['fd_usage_rate'] = 0
            
            return stats
            
        except Exception as e:
            log_error(f"获取系统统计信息时出错: {e}")
            return {}


# 全局监控器实例
_system_monitor = SystemMonitor()


def start_system_monitoring():
    """启动系统监控"""
    _system_monitor.start_monitoring()


def stop_system_monitoring():
    """停止系统监控"""
    _system_monitor.stop_monitoring()


def get_system_stats():
    """获取系统统计信息"""
    return _system_monitor.get_current_stats()
