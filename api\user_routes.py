# -*- coding: utf-8 -*-
from flask import Blueprint, request, jsonify
from database import query_db
from services import (
    get_user_by_api_key, get_user_usage_today, get_user_rate_limit,
    get_user_daily_usage_by_model, create_or_update_user, regenerate_api_key,
    add_gemini_key
)
from utils.logger import log_debug, log_warning, log_info, log_error

user_bp = Blueprint('user', __name__, url_prefix='/api')


@user_bp.route('/accountInfo', methods=['GET'])
def account_info():
    """获取用户账户信息 (使用情况、速率限制等)。"""
    log_debug("收到获取账户信息的请求 [/api/accountInfo]")
    api_key = request.args.get('key')
    if not api_key:
        log_warning("请求缺少 API 密钥参数 'key'")
        return jsonify({"error": "API key required"}), 401
    
    log_debug(f"使用 API 密钥 key=***{api_key[-4:]} 查询账户信息")
    user = get_user_by_api_key(api_key)
    if not user:
        return jsonify({"error": "Invalid API key"}), 401
    
    user_id = user['id']
    user_usage = get_user_usage_today(user_id)
    rate_limit = get_user_rate_limit(user_id)
    
    # 获取用户今日的分模型用量
    daily_usage = get_user_daily_usage_by_model(user_id)
    
    response_data = {
        "username": user['username'],
        "daily_usage": user_usage,
        "rate_limit": rate_limit,  # requests per minute
        "contributed_keys": daily_usage['contributed_keys'],
        "pro_model_usage": daily_usage['pro_model_usage'],
        "other_model_usage": daily_usage['other_model_usage'],
        "pro_model_limit": daily_usage['pro_model_limit'],
        "other_model_limit": daily_usage['other_model_limit']
    }
    log_info(f"返回用户 ID={user_id} 的账户信息: {response_data}")
    return jsonify(response_data)


@user_bp.route('/contribute', methods=['POST'])
def contribute_key():
    """用户贡献 Gemini API 密钥。"""
    log_info("收到贡献 Gemini 密钥的请求 [/api/contribute]")
    api_key = request.args.get('key')  # 用户自己的 API key
    if not api_key:
        log_warning("请求缺少用户 API 密钥参数 'key'")
        return jsonify({"error": "API key required"}), 401
        
    log_info(f"用户 (key=***{api_key[-4:]}) 尝试贡献密钥")
    user = get_user_by_api_key(api_key)
    if not user:
        return jsonify({"error": "Invalid API key"}), 401
        
    data = request.get_json()
    if not data or 'gemini_key' not in data:
        log_warning(f"用户 ID={user['id']} 的贡献请求缺少 'gemini_key' 字段")
        return jsonify({"error": "Gemini API key required in JSON payload"}), 400
        
    gemini_key_to_add = data['gemini_key'].strip()
    if not gemini_key_to_add:
        log_warning(f"用户 ID={user['id']} 的贡献请求提供了空的或只包含空格的 'gemini_key'。")
        return jsonify({"error": "Gemini API key cannot be empty or just whitespace"}), 400
    
    log_info(f"用户 ID={user['id']} 正在贡献 Gemini 密钥: key=***{gemini_key_to_add[-4:]}")

    # 调用添加密钥的函数
    success = add_gemini_key(gemini_key_to_add, user['id'])
    
    if success:
        log_info(f"用户 ID={user['id']} 成功贡献密钥 key=***{gemini_key_to_add[-4:]}")
        return jsonify({"success": True, "message": "密钥添加成功并通过验证！(Key added successfully and validated!)"})
    else:
        existing_key_info = query_db('SELECT id FROM api_keys WHERE key = ?', [gemini_key_to_add], one=True)
        if existing_key_info:
             return jsonify({"success": False, "message": "此 Gemini 密钥已存在于池中。(This Gemini key already exists in the pool.)"})
        else:
             return jsonify({"success": False, "message": "提供的 Gemini 密钥无效或无法验证。(Invalid or unverifiable Gemini key provided.)"})


@user_bp.route('/regenerateKey', methods=['POST'])
def regenerate_key():
    """重新生成用户的 API 密钥。"""
    log_info("收到重新生成 API 密钥的请求 [/api/regenerateKey]")
    api_key = request.args.get('key')  # 当前的 API key
    if not api_key:
        log_warning("请求缺少当前 API 密钥参数 'key'")
        return jsonify({"error": "API key required"}), 401
        
    log_info(f"尝试使用 API 密钥 key=***{api_key[-4:]} 重新生成密钥")
    user = get_user_by_api_key(api_key)
    if not user:
        return jsonify({"error": "Invalid API key"}), 401
        
    log_info(f"为用户 ID={user['id']} (Username={user['username']}) 重新生成密钥...")
    new_key = regenerate_api_key(user['id'])
    
    if new_key:
        log_info(f"用户 ID={user['id']} 的新 API 密钥已生成: key=***{new_key[-4:]}")
        return jsonify({"success": True, "new_key": new_key})
    else:
        log_error(f"为用户 ID={user['id']} 重新生成密钥失败。")
        return jsonify({"success": False, "message": "Failed to regenerate API key"}), 500


@user_bp.route('/register', methods=['POST'])
def register_user():
    """注册新用户或更新现有用户信息。"""
    log_info("收到注册/更新用户的请求 [/api/register]")
    data = request.get_json()
    required_fields = ['linuxdo_id', 'username', 'email', 'avatar_url']
    
    if not data or not all(field in data for field in required_fields):
        missing_fields = [field for field in required_fields if not data or field not in data]
        log_warning(f"注册请求缺少必填字段: {missing_fields}")
        return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400
        
    linuxdo_id = data['linuxdo_id']
    username = data['username']
    email = data['email']
    avatar_url = data['avatar_url']
    
    log_info(f"处理注册/更新请求: LinuxDo ID={linuxdo_id}, Username={username}")
    
    # 调用创建或更新用户的函数
    api_key = create_or_update_user(linuxdo_id, username, email, avatar_url)
    
    if api_key:
        log_info(f"用户注册/更新成功 (LinuxDo ID={linuxdo_id})。返回 API 密钥: key=***{api_key[-4:]}")
        return jsonify({"success": True, "api_key": api_key})
    else:
        log_error(f"处理用户注册/更新请求失败 (LinuxDo ID={linuxdo_id})。")
        return jsonify({"success": False, "message": "Failed to register or update user"}), 500


@user_bp.route('/contributorRank', methods=['GET'])
def contributor_rank():
    """获取贡献者排名。"""
    log_debug("收到获取贡献者排名的请求 [/api/contributorRank]")
    try:
        contributors_data = query_db("""
            SELECT
                users.username,
                users.avatar_url,
                COUNT(api_keys.id) AS key_count
            FROM
                users
            LEFT JOIN
                api_keys ON users.id = api_keys.contributor_id
            GROUP BY
                users.id, users.username, users.avatar_url
            ORDER BY
                key_count DESC
            LIMIT 20
        """)
        
        contributors_rank = []
        for row in contributors_data:
            contributors_rank.append({
                'username': row['username'],
                'avatar_url': row['avatar_url'],
                'key_count': row['key_count']
            })
        
        log_debug("成功获取贡献者排名并返回")
        return jsonify(contributors_rank)
    except Exception as e:
        log_error(f"获取贡献者排名时出错: {e}")
        return jsonify({"success": False, "message": "数据库查询失败"}), 500
