# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
from database import query_db
from utils.config import DEFAULT_RATE_LIMIT, CONTRIB_RATE_BONUS, RPD_PRO, CONTRIB_PRO_LIMIT, RPD_OTHER, CONTRB_OTHER_LIMIT
from utils.logger import log_debug, log_warning


def get_user_rate_limit(user_id):
    """计算用户的速率限制 (请求数/分钟)。"""
    log_debug(f"计算用户 ID={user_id} 的速率限制...")
    
    # 统计用户贡献的有效密钥数量
    contributed_keys = query_db(
        'SELECT COUNT(*) as count FROM api_keys WHERE contributor_id = ? AND is_valid = 1',
        [user_id], one=True
    )
    key_count = contributed_keys['count'] if contributed_keys else 0
    log_debug(f"用户 ID={user_id} 贡献了 {key_count} 个有效密钥。")
    
    # 基础速率 + 每个密钥的奖励
    user_limit = DEFAULT_RATE_LIMIT + (key_count * CONTRIB_RATE_BONUS)
    log_debug(f"用户 ID={user_id} 的计算速率限制为: {user_limit} 请求/分钟。")
    return user_limit


def check_rate_limit(user_id, modelName):
    """检查用户是否超出了速率限制。"""
    log_debug(f"检查用户 ID={user_id} 的速率限制...")
    
    # 检查每分钟速率限制
    one_minute_ago = (datetime.now() - timedelta(minutes=1)).strftime('%Y-%m-%d %H:%M:%S')
    recent_requests = query_db(
        'SELECT COUNT(*) as count FROM usage_logs WHERE user_id = ? AND request_time >= ?',
        [user_id, one_minute_ago], one=True
    )
    request_count = recent_requests['count'] if recent_requests else 0
    log_debug(f"用户 ID={user_id} 在过去一分钟内发起了 {request_count} 次请求。")
    
    # 获取用户的速率限制
    user_limit = get_user_rate_limit(user_id)
    
    # 比较请求次数和限制
    is_allowed = request_count < user_limit
    if is_allowed:
        log_debug(f"用户 ID={user_id} 未超速 ({request_count} < {user_limit})。允许请求。")
    else:
        log_warning(f"用户 ID={user_id} 已达到速率限制 ({request_count} >= {user_limit})。拒绝请求。")
        return False
    
    # 检查每日请求限制
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999).strftime('%Y-%m-%d %H:%M:%S')
    
    # 获取用户贡献的密钥数量
    contributed_keys = query_db(
        'SELECT COUNT(*) as count FROM api_keys WHERE contributor_id = ? AND is_valid = 1',
        [user_id], one=True
    )
    key_count = contributed_keys['count'] if contributed_keys else 0
    
    if "pro" in modelName:
        recent_requests = query_db(
            'SELECT COUNT(*) as count FROM usage_logs WHERE user_id = ? AND request_time BETWEEN ? AND ?  AND endpoint like ?',
            [user_id, today_start, today_end, f'%pro%'], one=True
        )
        request_count = recent_requests['count'] if recent_requests else 0
        daily_limit = RPD_PRO + key_count * CONTRIB_PRO_LIMIT
        is_allowed = request_count < daily_limit
        log_debug(f"用户 ID={user_id} 的 Pro 模型请求限制: {daily_limit}, 今日已用: {request_count}")
    else:
        recent_requests = query_db(
            'SELECT COUNT(*) as count FROM usage_logs WHERE user_id = ? AND request_time BETWEEN ? AND ? AND endpoint like \'%model%\' AND endpoint not like ?',
            [user_id, today_start, today_end, f'%pro%'], one=True
        )
        request_count = recent_requests['count'] if recent_requests else 0
        daily_limit = RPD_OTHER + key_count * CONTRB_OTHER_LIMIT
        is_allowed = request_count < daily_limit
        log_debug(f"用户 ID={user_id} 的其他模型请求限制: {daily_limit}, 今日已用: {request_count}")
    
    return is_allowed


def get_user_daily_usage_by_model(user_id):
    """获取用户今日按模型分类的使用情况"""
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999).strftime('%Y-%m-%d %H:%M:%S')
    
    # Pro 模型使用量
    recent_requests_pro = query_db(
        'SELECT COUNT(*) as count FROM usage_logs WHERE user_id = ? AND request_time BETWEEN ? AND ?  AND endpoint like \'%pro%\'',
        [user_id, today_start, today_end], one=True
    )
    request_count_pro = recent_requests_pro['count'] if recent_requests_pro else 0
    
    # 其他模型使用量
    recent_requests_other = query_db(
        'SELECT COUNT(*) as count FROM usage_logs WHERE user_id = ? AND request_time BETWEEN ? AND ?  AND endpoint like \'%models%\' AND endpoint not LIKE \'%pro%\'',
        [user_id, today_start, today_end], one=True
    )
    request_count_other = recent_requests_other['count'] if recent_requests_other else 0
    
    # 获取用户贡献的密钥数量
    contributed_keys = query_db(
        'SELECT COUNT(*) as count FROM api_keys WHERE contributor_id = ? AND is_valid = 1',
        [user_id], one=True
    )
    key_count = contributed_keys['count'] if contributed_keys else 0
    
    return {
        'pro_model_usage': request_count_pro,
        'other_model_usage': request_count_other,
        'pro_model_limit': RPD_PRO + key_count * CONTRIB_PRO_LIMIT,
        'other_model_limit': RPD_OTHER + key_count * CONTRB_OTHER_LIMIT,
        'contributed_keys': key_count
    }
