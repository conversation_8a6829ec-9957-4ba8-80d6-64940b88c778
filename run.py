# -*- coding: utf-8 -*-
"""
应用启动脚本
提供更灵活的启动选项
"""
import sys
import time
import argparse
from flask import Flask
from flask_cors import CORS

# 导入重构后的模块
from config import get_config
from utils import log_info, log_error, log_warning, set_logging_enabled, set_log_level
from database import create_tables, close_connection
from services import (
    update_gemini_keys, invalidate_keys_with_whitespace, check_all_keys
)
from api import register_blueprints


def create_app(config_class=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_class is None:
        config_class = get_config()
    app.config.from_object(config_class)

    # 启用CORS
    CORS(app)

    # 注册数据库连接关闭处理器
    app.teardown_appcontext(close_connection)

    # 注册所有蓝图（包括健康检查和错误处理器）
    register_blueprints(app)

    return app


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Gemini API 代理服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--skip-validation', action='store_true', help='跳过启动时的密钥验证')
    parser.add_argument('--disable-log', action='store_true', help='禁用日志记录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='设置日志级别')
    parser.add_argument('--env', choices=['development', 'production', 'testing'], 
                       default='development', help='运行环境')
    
    return parser.parse_args()


def initialize_app(app, skip_validation=False):
    """初始化应用"""
    log_info("应用启动程序开始...")
    
    # 在应用启动时创建表
    create_tables()
    
    # 1. 首次从数据库更新 key 池
    with app.app_context():
        log_info("执行启动时的首次密钥池更新...")
        update_gemini_keys()

    # 2. 检查并使包含空格的密钥无效
    log_info("执行启动时的空格密钥检查...")
    invalidate_keys_with_whitespace()

    # 3. 再次更新 key 池，移除因空格而失效的 key
    with app.app_context():
        log_info("空格检查后再次更新密钥池...")
        update_gemini_keys()

    # 4. 根据参数决定是否执行 API 验证
    if not skip_validation and False:
        log_info("执行启动时的密钥验证...")
        with app.app_context():
            start_check_time = time.time()
            check_all_keys()
            check_duration = time.time() - start_check_time
            log_info(f"启动时 API 密钥验证完成，耗时: {check_duration:.2f} 秒。")
            # 5. API 验证结束后，再更新一次 key 池
            log_info("API 密钥验证后再次更新密钥池...")
            update_gemini_keys()
    else:
        log_info("跳过启动时的 API 密钥验证。")


def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志
    if args.disable_log:
        set_logging_enabled(False)
        print("日志记录已禁用。")
    else:
        set_log_level(getattr(__import__('logging'), args.log_level))
        log_info("日志记录已启用。")

    # 设置环境变量
    import os
    os.environ['FLASK_ENV'] = args.env

    # 创建应用
    app = create_app()

    # 初始化应用
    initialize_app(app, skip_validation=args.skip_validation)

    # 启动系统监控
    try:
        from utils.system_monitor import start_system_monitoring
        start_system_monitoring()
        log_info("系统监控已启动")
    except Exception as e:
        log_warning(f"启动系统监控失败: {e}")

    # 启动服务器
    log_info(f"准备在 {args.host}:{args.port} 上启动 Flask 服务器...")

    try:
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
    except Exception as e:
        log_error(f"启动 Flask 服务器时发生错误: {e}", exc_info=True)
    finally:
        # 清理资源
        try:
            from utils.system_monitor import stop_system_monitoring
            from utils.resource_manager import cleanup_resources
            stop_system_monitoring()
            cleanup_resources()
            log_info("资源清理完成")
        except Exception as e:
            log_error(f"资源清理时出错: {e}")
        log_info("Flask 应用已停止。")


if __name__ == '__main__':
    main()
