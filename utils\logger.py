# -*- coding: utf-8 -*-
import sys
import logging


# 全局标志，用于控制是否启用日志记录 (Global flag to control logging)
LOGGING_ENABLED = True

# 创建一个 logger 实例 (Create a logger instance)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)  # 设置默认日志级别 (Set default log level)

# 创建一个控制台处理器 (Create a console handler)
handler = logging.StreamHandler(sys.stdout)
handler.setLevel(logging.INFO)  # 设置处理器级别 (Set handler level)

# 创建一个日志格式器 (Create a log formatter)
formatter = logging.Formatter(
    '%(asctime)s | %(levelname)s | %(filename)s:%(lineno)d | %(funcName)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
handler.setFormatter(formatter)

# 将处理器添加到 logger (Add the handler to the logger)
logger.addHandler(handler)


# 包装日志函数以检查 LOGGING_ENABLED 标志 (Wrap logging functions to check LOGGING_ENABLED flag)
def log_info(message, *args, **kwargs):
    """记录 INFO 级别的日志 (Log an INFO level message)"""
    if LOGGING_ENABLED:
        logger.info(message, *args, **kwargs)


def log_warning(message, *args, **kwargs):
    """记录 WARNING 级别的日志 (Log a WARNING level message)"""
    if LOGGING_ENABLED:
        logger.warning(message, *args, **kwargs)


def log_error(message, *args, **kwargs):
    """记录 ERROR 级别的日志 (Log an ERROR level message)"""
    if LOGGING_ENABLED:
        logger.error(message, *args, **kwargs)


def log_debug(message, *args, **kwargs):
    """记录 DEBUG 级别的日志 (Log a DEBUG level message)"""
    if LOGGING_ENABLED:
        logger.debug(message, *args, **kwargs)


def set_logging_enabled(enabled):
    """设置是否启用日志记录"""
    global LOGGING_ENABLED
    LOGGING_ENABLED = enabled


def set_log_level(level):
    """设置日志级别"""
    logger.setLevel(level)
    handler.setLevel(level)
