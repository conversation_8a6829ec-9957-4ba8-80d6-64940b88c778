# -*- coding: utf-8 -*-
"""
资源管理模块
解决文件描述符泄漏问题
"""
import requests
import sqlite3
import threading
import time
from contextlib import contextmanager
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
from utils.logger import log_debug, log_warning, log_error
from utils.config import DATABASE


class ConnectionPoolManager:
    """HTTP连接池管理器"""
    
    def __init__(self):
        self._session = None
        self._lock = threading.Lock()
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
    
    def get_session(self):
        """获取配置好的requests session"""
        with self._lock:
            if self._session is None:
                self._session = self._create_session()
            
            # 定期清理连接池
            current_time = time.time()
            if current_time - self._last_cleanup > self._cleanup_interval:
                self._cleanup_connections()
                self._last_cleanup = current_time
            
            return self._session
    
    def _create_session(self):
        """创建配置好的session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 配置HTTP适配器
        adapter = HTTPAdapter(
            pool_connections=10,  # 连接池数量
            pool_maxsize=20,      # 每个连接池的最大连接数
            max_retries=retry_strategy,
            pool_block=False      # 不阻塞，避免死锁
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认超时
        session.timeout = 30
        
        log_debug("创建了新的HTTP连接池")
        return session
    
    def _cleanup_connections(self):
        """清理连接池"""
        if self._session:
            try:
                # 关闭所有连接
                self._session.close()
                log_debug("清理了HTTP连接池")
                # 重新创建session
                self._session = self._create_session()
            except Exception as e:
                log_error(f"清理连接池时出错: {e}")
    
    def close(self):
        """关闭连接池"""
        with self._lock:
            if self._session:
                self._session.close()
                self._session = None
                log_debug("关闭了HTTP连接池")


# 全局连接池管理器
_connection_pool = ConnectionPoolManager()


def get_http_session():
    """获取HTTP session"""
    return _connection_pool.get_session()


@contextmanager
def safe_http_request(method, url, **kwargs):
    """安全的HTTP请求上下文管理器"""
    response = None
    try:
        session = get_http_session()
        
        # 确保设置超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30
        
        # 确保关闭连接
        if 'headers' not in kwargs:
            kwargs['headers'] = {}
        kwargs['headers']['Connection'] = 'close'
        
        log_debug(f"发起HTTP请求: {method} {url}")
        response = session.request(method, url, **kwargs)
        yield response
        
    except Exception as e:
        log_error(f"HTTP请求失败: {method} {url}, 错误: {e}")
        raise
    finally:
        if response is not None:
            try:
                response.close()
                log_debug(f"关闭HTTP响应: {method} {url}")
            except Exception as e:
                log_warning(f"关闭HTTP响应时出错: {e}")


@contextmanager
def safe_streaming_request(method, url, **kwargs):
    """安全的流式HTTP请求上下文管理器"""
    response = None
    try:
        session = get_http_session()
        
        # 流式请求配置
        kwargs['stream'] = True
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 600  # 流式请求需要更长超时
        
        log_debug(f"发起流式HTTP请求: {method} {url}")
        response = session.request(method, url, **kwargs)
        yield response
        
    except Exception as e:
        log_error(f"流式HTTP请求失败: {method} {url}, 错误: {e}")
        raise
    finally:
        if response is not None:
            try:
                response.close()
                log_debug(f"关闭流式HTTP响应: {method} {url}")
            except Exception as e:
                log_warning(f"关闭流式HTTP响应时出错: {e}")


class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, database_path, max_connections=10):
        self.database_path = database_path
        self.max_connections = max_connections
        self._connections = []
        self._lock = threading.Lock()
        self._created_connections = 0
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            conn = self._acquire_connection()
            yield conn
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            if conn:
                self._release_connection(conn)
    
    def _acquire_connection(self):
        """获取连接"""
        with self._lock:
            # 尝试从池中获取连接
            if self._connections:
                conn = self._connections.pop()
                log_debug("从连接池获取数据库连接")
                return conn
            
            # 如果池为空且未达到最大连接数，创建新连接
            if self._created_connections < self.max_connections:
                conn = sqlite3.connect(self.database_path, check_same_thread=False)
                conn.row_factory = sqlite3.Row
                self._created_connections += 1
                log_debug(f"创建新的数据库连接 ({self._created_connections}/{self.max_connections})")
                return conn
            
            # 如果达到最大连接数，等待并重试
            log_warning("数据库连接池已满，等待可用连接")
        
        # 简单的等待重试机制
        time.sleep(0.1)
        return self._acquire_connection()
    
    def _release_connection(self, conn):
        """释放连接"""
        try:
            # 检查连接是否还有效
            conn.execute('SELECT 1')
            
            with self._lock:
                if len(self._connections) < self.max_connections:
                    self._connections.append(conn)
                    log_debug("归还数据库连接到连接池")
                else:
                    conn.close()
                    self._created_connections -= 1
                    log_debug("关闭多余的数据库连接")
        except Exception as e:
            log_error(f"释放数据库连接时出错: {e}")
            try:
                conn.close()
                with self._lock:
                    self._created_connections -= 1
            except:
                pass
    
    def close_all(self):
        """关闭所有连接"""
        with self._lock:
            for conn in self._connections:
                try:
                    conn.close()
                except:
                    pass
            self._connections.clear()
            self._created_connections = 0
            log_debug("关闭了所有数据库连接")


# 全局数据库连接池
_db_pool = DatabaseConnectionPool(DATABASE)


def get_db_connection():
    """获取数据库连接上下文管理器"""
    return _db_pool.get_connection()


def cleanup_resources():
    """清理所有资源"""
    log_debug("开始清理所有资源...")
    _connection_pool.close()
    _db_pool.close_all()
    log_debug("资源清理完成")


# 注册清理函数
import atexit
atexit.register(cleanup_resources)
