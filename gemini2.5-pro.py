# -*- coding: utf-8 -*-
"""
Gemini API 代理服务器
重构后的主应用文件，只负责应用启动和基础配置
"""
import sys
import time
from flask import Flask
from flask_cors import CORS

# 导入重构后的模块
from utils import log_info, log_error, set_logging_enabled
from database import create_tables, close_connection
from services import (
    update_gemini_keys, invalidate_keys_with_whitespace, check_all_keys
)
from api import register_blueprints


# ==============================================================================
# Flask 应用设置 (Flask Application Setup)
# ==============================================================================
log_info("初始化 Flask 应用...")
app = Flask(__name__)
CORS(app)  # 为前端集成启用 CORS
log_info("Flask 应用已启用 CORS。")

# 注册数据库连接关闭处理器
app.teardown_appcontext(close_connection)

# 注册所有蓝图
register_blueprints(app)
log_info("所有API路由已注册。")


# ==============================================================================
# 应用启动入口 (Application Entry Point)
# ==============================================================================
if __name__ == '__main__':
    # 检查命令行参数是否需要禁用日志
    if '--disable-log' in sys.argv:
        set_logging_enabled(False)
        print("日志记录已禁用。(--disable-log detected, logging disabled.)")
    else:
        log_info("日志记录已启用。使用 --disable-log 命令行参数可禁用。")

    log_info("应用启动程序开始...")

    # 在应用启动时创建表
    create_tables()

    # 1. 首次从数据库更新 key 池
    with app.app_context():
        log_info("执行启动时的首次密钥池更新...")
        update_gemini_keys()

    # 2. 检查并使包含空格的密钥无效
    log_info("执行启动时的空格密钥检查...")
    invalidate_keys_with_whitespace()

    # 3. 再次更新 key 池，移除因空格而失效的 key
    with app.app_context():
        log_info("空格检查后再次更新密钥池...")
        update_gemini_keys()

    # 4. 根据 --skip 参数决定是否执行 API 验证
    if '--skip' not in sys.argv and False:
        log_info("未指定 --skip，将执行启动时的密钥验证...")
        with app.app_context():
            start_check_time = time.time()
            check_all_keys()
            check_duration = time.time() - start_check_time
            log_info(f"启动时 API 密钥验证完成，耗时: {check_duration:.2f} 秒。")
            # 5. API 验证结束后，再更新一次 key 池
            log_info("API 密钥验证后再次更新密钥池...")
            update_gemini_keys()
    else:
        log_info("检测到 --skip 参数，跳过启动时的 API 密钥验证。")

    # 获取运行端口和主机
    run_host = '0.0.0.0'
    run_port = 5000
    log_info(f"准备在 {run_host}:{run_port} 上启动 Flask 开发服务器...")

    # 启动 Flask 应用
    try:
        app.run(host=run_host, port=run_port)
    except Exception as e:
        log_error(f"启动 Flask 服务器时发生错误: {e}", exc_info=True)
    finally:
        log_info("Flask 应用已停止。")