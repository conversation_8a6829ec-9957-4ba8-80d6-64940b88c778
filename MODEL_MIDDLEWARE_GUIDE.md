# 模型中间件使用指南

## 概述

模型中间件允许您通过在模型名称后添加功能后缀来扩展Gemini API的功能。使用 `#` 作为分隔符来分隔基础模型名和功能后缀。

## 基本语法

```
基础模型名#功能1#功能2#...
```

例如：
- `gemini-2.5-pro#search` - 在基础模型上启用搜索功能
- `gemini-2.5-flash#search#fast` - 同时启用搜索和快速模式

## 可用功能

### 1. 搜索功能 (`search`)
启用Google搜索功能，自动添加搜索工具到请求中。

**示例：**
```
gemini-2.5-pro#search
```

**效果：**
- 自动添加 `{"google_search": {}}` 工具
- 模型可以进行实时网络搜索

### 2. 视觉功能 (`vision`)
启用视觉分析功能（主要用于确保使用支持视觉的模型）。

**示例：**
```
gemini-2.5-flash#vision
```

### 3. 快速模式 (`fast`)
优化参数以获得更快的响应速度。

**示例：**
```
gemini-2.5-pro#fast
```

**效果：**
- 设置 `maxOutputTokens: 1024`
- 设置 `temperature: 0.7`

### 4. 专业模式 (`pro`)
优化参数以获得更高质量的输出。

**示例：**
```
gemini-2.5-pro#pro
```

**效果：**
- 设置 `maxOutputTokens: 8192`
- 设置 `temperature: 0.3`
- 设置 `topP: 0.8`
- 设置 `topK: 40`

## 组合使用

您可以组合多个功能：

```
gemini-2.5-pro#search#fast
```

这将同时启用搜索功能和快速模式。

## URL使用

在HTTP请求中使用时，需要将 `#` 编码为 `%23`：

```bash
curl -X POST \
  "https://your-api.com/v1beta/models/gemini-2.5-pro%23search:generateContent" \
  -H "Content-Type: application/json" \
  -H "X-Goog-Api-Key: YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "What is the current weather in Tokyo?"
          }
        ]
      }
    ]
  }'
```

## 实际示例

### 1. 基础查询
```bash
# 普通查询
POST /v1beta/models/gemini-2.5-pro:generateContent
```

### 2. 带搜索的查询
```bash
# 启用搜索功能
POST /v1beta/models/gemini-2.5-pro%23search:generateContent
```

### 3. 快速搜索查询
```bash
# 搜索 + 快速模式
POST /v1beta/models/gemini-2.5-flash%23search%23fast:generateContent
```

### 4. 专业分析
```bash
# 专业模式，适合复杂分析任务
POST /v1beta/models/gemini-2.5-pro%23pro:generateContent
```

## 扩展开发

### 添加自定义处理器

您可以通过扩展系统添加自定义功能：

```python
from utils.model_middleware import register_model_processor

def custom_processor(base_model, payload):
    """自定义处理器"""
    # 添加自定义逻辑
    payload['customParameter'] = 'value'
    return payload

# 注册处理器
register_model_processor('custom', custom_processor)
```

### 使用扩展管理器

```python
from utils.extension_manager import register_extension, BaseExtension

class MyExtension(BaseExtension):
    def middleware_custom_filter(self, data):
        """自定义中间件"""
        return data
    
    def hook_before_request(self, request_data):
        """请求前钩子"""
        return request_data

# 注册扩展
register_extension('MyExtension', MyExtension)
```

## 管理后台

访问 `/admin` 可以查看：
- 所有可用的模型处理器
- 已加载的扩展
- 系统状态和统计信息

## 注意事项

1. **URL编码**：在URL中使用时，`#` 必须编码为 `%23`
2. **顺序**：多个功能按照指定顺序依次应用
3. **兼容性**：确保基础模型支持所请求的功能
4. **性能**：某些功能组合可能影响响应速度

## 故障排除

### 常见问题

1. **功能不生效**
   - 检查是否正确使用了 `#` 分隔符
   - 确认功能名称拼写正确
   - 查看服务器日志了解详细错误信息

2. **URL解析错误**
   - 确保在URL中使用 `%23` 而不是 `#`
   - 检查URL编码是否正确

3. **功能冲突**
   - 某些功能组合可能不兼容
   - 查看管理后台的处理器信息

### 调试

启用调试模式查看详细日志：

```bash
python run.py --debug --log-level DEBUG
```

这将显示中间件处理的详细信息，帮助您诊断问题。
