# -*- coding: utf-8 -*-
"""
健康检查和监控端点
"""
from flask import Blueprint, jsonify
import time
from datetime import datetime
from services import gemini_key_pool
from database import get_direct_db_connection
from utils.logger import log_debug

health_bp = Blueprint('health', __name__, url_prefix='/health')

# 应用启动时间
start_time = time.time()


@health_bp.route('/', methods=['GET'])
def health_check():
    """基础健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'uptime': time.time() - start_time
    })


@health_bp.route('/detailed', methods=['GET'])
def detailed_health_check():
    """详细健康检查"""
    log_debug("执行详细健康检查...")
    
    # 检查数据库连接
    db_status = 'healthy'
    try:
        db = get_direct_db_connection()
        if db:
            cursor = db.cursor()
            cursor.execute('SELECT 1')
            cursor.fetchone()
            db.close()
        else:
            db_status = 'unhealthy'
    except Exception as e:
        log_debug(f"数据库健康检查失败: {e}")
        db_status = 'unhealthy'
    
    # 检查密钥池状态
    key_pool_status = 'healthy' if len(gemini_key_pool) > 0 else 'warning'
    
    return jsonify({
        'status': 'healthy' if db_status == 'healthy' and key_pool_status == 'healthy' else 'degraded',
        'timestamp': datetime.now().isoformat(),
        'uptime': time.time() - start_time,
        'components': {
            'database': {
                'status': db_status
            },
            'key_pool': {
                'status': key_pool_status,
                'available_keys': len(gemini_key_pool)
            }
        }
    })


@health_bp.route('/metrics', methods=['GET'])
def metrics():
    """系统指标"""
    try:
        from utils.system_monitor import get_system_stats
        system_stats = get_system_stats()
    except Exception as e:
        log_debug(f"获取系统统计信息失败: {e}")
        system_stats = {}

    return jsonify({
        'timestamp': datetime.now().isoformat(),
        'uptime': time.time() - start_time,
        'key_pool': {
            'total_keys': len(gemini_key_pool),
            'available_keys': len(gemini_key_pool)
        },
        'system': system_stats
    })
