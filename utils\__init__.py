# -*- coding: utf-8 -*-
"""
工具模块
包含线程安全队列、日志配置、配置常量等工具类
"""

from .thread_safe_queue import ThreadSafeListQueue, QueueEmpty, QueueFull
from .logger import log_info, log_warning, log_error, log_debug, set_logging_enabled, set_log_level
from .config import *
from .resource_manager import (
    get_http_session, safe_http_request, safe_streaming_request,
    get_db_connection, cleanup_resources
)

__all__ = [
    'ThreadSafeListQueue', 'QueueEmpty', 'QueueFull',
    'log_info', 'log_warning', 'log_error', 'log_debug', 'set_logging_enabled', 'set_log_level',
    'DATABASE', 'GEMINI_API_BASE', 'DEFAULT_RATE_LIMIT', 'CONTRIB_RATE_BONUS',
    'RPD_PRO', 'CONTRIB_PRO_LIMIT', 'RPD_OTHER', 'CONTRB_OTHER_LIMIT',
    'DEFAULT_SAFETY_SETTINGS',
    'get_http_session', 'safe_http_request', 'safe_streaming_request',
    'get_db_connection', 'cleanup_resources'
]
