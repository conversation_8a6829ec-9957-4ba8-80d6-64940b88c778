# -*- coding: utf-8 -*-
"""
配置常量模块
包含应用程序的所有配置常量
"""

# ==============================================================================
# 数据库配置 (Database Configuration)
# ==============================================================================
DATABASE = 'geminipool.db'  # 数据库文件名 (Database file name)

# ==============================================================================
# Gemini API 配置 (Gemini API Configuration)
# ==============================================================================
# Google Gemini API 基础端点 (Google Gemini API base endpoint)
GEMINI_API_BASE = "https://generativelanguage.googleapis.com"

# ==============================================================================
# 速率限制配置 (Rate Limiting Configuration)
# ==============================================================================
# 每分钟默认请求限制 (Default request limit per minute)
DEFAULT_RATE_LIMIT = 5

# 每个贡献密钥的额外请求奖励 (Extra requests per contributed key)
CONTRIB_RATE_BONUS = 20

# 每日请求限制 (Request per day limit)
RPD_PRO = 50  # Pro 模型每日请求限制
CONTRIB_PRO_LIMIT = 100  # 贡献者 Pro 模型额外限制
RPD_OTHER = 5000  # 其他模型每日请求限制
CONTRB_OTHER_LIMIT = 10000  # 贡献者其他模型额外限制

# ==============================================================================
# 安全设置 (Safety Settings)
# ==============================================================================
# 默认安全设置 (Default safety settings)
DEFAULT_SAFETY_SETTINGS = {
    "safetySettings": [
        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "OFF"},
        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "OFF"},
        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "OFF"},
        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "OFF"},
        {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "OFF"}  # Google 新增的分类
    ]
}
