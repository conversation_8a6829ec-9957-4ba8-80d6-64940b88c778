
from flask import Flask, request, jsonify, Response
import requests
import random

app = Flask(__name__)

# Gemini API Key 池
gemini_key_pool = "AIzaSyD-ThIfr0VPxQwN91l0y8w6ggKgeQGNPLQ,AIzaSyBHxo3VjhAqYtr7CXDQH96MJA5IKC8TX6I,AIzaSyDlpR-zZ8zw43CsnSdGrIEeezBuKjth10w,AIzaSyAlRKnbiiYUX0zK50OspHEfHINhZi0dnZY,AIzaSyAcNK8ebpW3GhtnsDRYXwRAl5gtpf0GBdQ,AIzaSyBGMBM2GBQObJLSq4isIiAa8qb5fMsqGNg,AIzaSyDM0vPWpAVBX7qcFXM0OVC9D_YZ2AYvfhw,AIzaSyBcP7KKnePrCY5AlwWn2jvjEYgSA8Oby5o".split(",")
# 用户 Key 数据库（模拟）
user_keys = {"key": {"user_id": "user1", "quota": 1000}}
# Google Gemini API 基础端点
GEMINI_API_BASE = "https://generativelanguage.googleapis.com"

# 默认安全设置：关闭所有过滤
DEFAULT_SAFETY_SETTINGS = {
    "safetySettings": [
        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "BLOCK_NONE"}
    ]
}

from flask import Flask, request, jsonify, Response
import requests

app = Flask(__name__)

def validate_user_key(user_key):
    return user_key in user_keys  # Simple validation, replace with database check if needed

def get_available_gemini_key():
   return random.choice(gemini_key_pool)

def stream_response(method, google_url, payload, headers):
    """Generator function to stream SSE responses from Google API"""
    with requests.request(
        method=method,
        url=google_url,
        json=payload if payload else None,
        headers=headers,
        stream=True,
        timeout=30  # 增加超时时间
    ) as response:
        response.raise_for_status()
        
        # 直接转发原始响应内容，不做任何修改
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                yield chunk

@app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def proxy_all(path):
    # Extract user-provided key parameter
    user_key = request.args.get('key')
    # if not user_key or not validate_user_key(user_key):
    #     return jsonify({"error": "Invalid or missing 'key' parameter"}), 401

    # Construct full Google API URL, preserving original query parameters
    google_url = f"{GEMINI_API_BASE}/{path}"
    query_params = request.args.to_dict()
    query_params['key'] = get_available_gemini_key()  # Replace with real Google API key
    query_string = '&'.join(f"{k}={v}" for k, v in query_params.items())
    google_url += f"?{query_string}"

    # Get request payload (if any)
    payload = request.get_json(silent=True)

    # Apply default safety settings for POST requests with content
    if request.method == 'POST' and payload:
        if "contents" in payload and "safetySettings" not in payload:
            payload = {**payload, **DEFAULT_SAFETY_SETTINGS}

    # 复制原始请求头，保留内容类型和认证信息
    headers = {k: v for k, v in request.headers.items() 
               if k.lower() in ['content-type', 'authorization']}
    
    # 确保 content-type 正确
    if payload:
        headers["Content-Type"] = "application/json"

    # Check if this is a streaming endpoint
    is_streaming = "streamGenerateContent" in path and request.args.get('alt') == 'sse'

    if is_streaming:
        # Stream the response using the generator, passing the method
        return Response(
            stream_response(request.method, google_url, payload, headers),
            # 保留原始响应的头信息，特别是content-type
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'  # 对于 Nginx 禁用缓冲
            }
        )
    else:
        # Handle non-streaming requests
        try:
            response = requests.request(
                method=request.method,
                url=google_url,
                json=payload if payload else None,
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            
            # 返回完整的响应，包括所有头信息
            return Response(
                response.content,
                status=response.status_code,
                mimetype=response.headers.get('Content-Type', 'application/json')
            )
        except requests.exceptions.RequestException as e:
            return jsonify({"error": f"Failed to connect to Gemini API: {str(e)}"}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)