# Gemini API 代理服务器

一个高性能的 Gemini API 代理服务器，支持密钥池管理、速率限制、用户认证等功能。

## 🚀 特性

- **模块化架构**: 清晰的代码结构，易于维护和扩展
- **密钥池管理**: 自动轮换和验证 Gemini API 密钥
- **用户认证**: 基于 API 密钥的用户管理系统
- **速率限制**: 灵活的速率限制策略
- **健康检查**: 内置健康检查和监控端点
- **错误处理**: 全局错误处理和日志记录
- **配置管理**: 支持环境变量和多环境配置

## 📁 项目结构

```
GeminiProxy/
├── api/                    # API 路由模块
│   ├── admin_routes.py     # 管理员路由
│   ├── user_routes.py      # 用户路由
│   ├── system_routes.py    # 系统统计路由
│   ├── proxy_routes.py     # 代理路由
│   ├── health_routes.py    # 健康检查路由
│   └── error_handlers.py   # 错误处理器
├── database/               # 数据库模块
│   ├── db_manager.py       # 数据库连接管理
│   └── models.py           # 数据库表模型
├── services/               # 业务逻辑模块
│   ├── user_service.py     # 用户管理服务
│   ├── key_service.py      # 密钥管理服务
│   └── rate_limit_service.py # 速率限制服务
├── utils/                  # 工具模块
│   ├── thread_safe_queue.py # 线程安全队列
│   ├── logger.py           # 日志配置
│   └── config.py           # 配置常量
├── config.py               # 应用配置管理
├── run.py                  # 新的启动脚本
├── gemini2.5-pro.py        # 原始启动文件（简化版）
└── README.md               # 项目文档
```

## 🛠️ 安装和运行

### 环境要求

- Python 3.7+
- Flask
- Flask-CORS
- Requests
- SQLite3

### 安装依赖

```bash
pip install flask flask-cors requests
```

### 运行应用

#### 使用新的启动脚本（推荐）

```bash
# 开发模式
python run.py --env development --debug

# 生产模式
python run.py --env production --host 0.0.0.0 --port 5000

# 跳过密钥验证（快速启动）
python run.py --skip-validation

# 自定义日志级别
python run.py --log-level DEBUG

# 禁用日志
python run.py --disable-log
```

#### 使用原始启动文件

```bash
# 基本启动
python gemini2.5-pro.py

# 跳过密钥验证
python gemini2.5-pro.py --skip

# 禁用日志
python gemini2.5-pro.py --disable-log
```

## 📊 API 端点

### 健康检查
- `GET /health/` - 基础健康检查
- `GET /health/detailed` - 详细健康检查
- `GET /health/metrics` - 系统指标

### 用户管理
- `GET /api/accountInfo` - 获取账户信息
- `POST /api/register` - 注册用户
- `POST /api/contribute` - 贡献密钥
- `POST /api/regenerateKey` - 重新生成API密钥
- `GET /api/contributorRank` - 贡献者排名

### 系统统计
- `GET /api/usage` - 全局使用统计

### 管理员功能
- `GET /api/admin/usageTotal` - 用户使用统计
- `POST /api/admin/withdrawKeys` - 撤回用户密钥

### 代理功能
- `/<path:path>` - 代理所有 Gemini API 请求

## ⚙️ 配置

### 环境变量

```bash
# 服务器配置
HOST=0.0.0.0
PORT=5000
FLASK_DEBUG=false

# 数据库配置
DATABASE_PATH=geminipool.db

# 日志配置
LOG_LEVEL=INFO
LOGGING_ENABLED=true

# API配置
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com

# 速率限制配置
DEFAULT_RATE_LIMIT=5
CONTRIB_RATE_BONUS=20
RPD_PRO=50
RPD_OTHER=5000
```

## 🔧 开发

### 添加新的API端点

1. 在相应的路由文件中添加新端点
2. 在 `api/__init__.py` 中注册蓝图
3. 添加相应的业务逻辑到 `services/` 模块

### 数据库操作

使用 `database/db_manager.py` 中的函数进行数据库操作：

```python
from database import query_db, execute_db

# 查询数据
users = query_db('SELECT * FROM users WHERE active = ?', [True])

# 执行操作
execute_db('UPDATE users SET last_login = ? WHERE id = ?', [datetime.now(), user_id])
```

## 📝 更新日志

### v2.0.0 - 重构版本
- 🔄 完全重构代码结构，提高可维护性
- 📁 模块化设计，清晰的职责分离
- 🏥 添加健康检查端点
- 🛡️ 改进错误处理
- ⚙️ 灵活的配置管理
- 📊 更好的日志记录

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
