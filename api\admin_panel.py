# -*- coding: utf-8 -*-
"""
管理员后台面板
提供简单的HTML界面进行用户和密钥管理
"""
from flask import Blueprint, request, render_template_string, jsonify, redirect, url_for
from database import query_db, execute_db
from services import get_user_by_api_key
from utils.logger import log_info, log_warning, log_error

admin_panel_bp = Blueprint('admin_panel', __name__, url_prefix='/admin')

# HTML模板
ADMIN_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Gemini Proxy - 管理员后台</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        input, button { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .delete-btn { background-color: #dc3545; }
        .delete-btn:hover { background-color: #c82333; }
        .form-group { margin: 10px 0; }
        .form-group label { display: inline-block; width: 120px; font-weight: bold; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .key-cell { max-width: 200px; word-break: break-all; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Gemini Proxy 管理员后台</h1>
        
        {% if message %}
        <div class="alert alert-{{ message_type }}">{{ message }}</div>
        {% endif %}
        
        <!-- 用户列表 -->
        <div class="section">
            <h2>👥 用户列表</h2>
            <form method="GET">
                <input type="hidden" name="action" value="list_users">
                <button type="submit">刷新用户列表</button>
            </form>
            
            {% if users %}
            <table>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>LinuxDo ID</th>
                    <th>邮箱</th>
                    <th>API Key</th>
                    <th>贡献密钥数</th>
                    <th>注册时间</th>
                </tr>
                {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.username or 'N/A' }}</td>
                    <td>{{ user.linuxdo_id or 'N/A' }}</td>
                    <td>{{ user.email or 'N/A' }}</td>
                    <td class="key-cell">{{ user.api_key[:20] }}...</td>
                    <td>{{ user.key_count }}</td>
                    <td>{{ user.created_at }}</td>
                </tr>
                {% endfor %}
            </table>
            {% endif %}
        </div>
        
        <!-- 用户查询 -->
        <div class="section">
            <h2>🔍 用户查询</h2>
            <form method="GET">
                <input type="hidden" name="action" value="search_user">
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" name="username" placeholder="输入用户名">
                    <button type="submit">按用户名查询</button>
                </div>
            </form>
            
            <form method="GET">
                <input type="hidden" name="action" value="search_user_by_id">
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="number" name="user_id" placeholder="输入用户ID">
                    <button type="submit">按ID查询</button>
                </div>
            </form>
            
            {% if search_result %}
            <h3>查询结果:</h3>
            <table>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>LinuxDo ID</th>
                    <th>邮箱</th>
                    <th>API Key</th>
                    <th>注册时间</th>
                </tr>
                <tr>
                    <td>{{ search_result.id }}</td>
                    <td>{{ search_result.username or 'N/A' }}</td>
                    <td>{{ search_result.linuxdo_id or 'N/A' }}</td>
                    <td>{{ search_result.email or 'N/A' }}</td>
                    <td class="key-cell">{{ search_result.api_key }}</td>
                    <td>{{ search_result.created_at }}</td>
                </tr>
            </table>
            {% endif %}
        </div>
        
        <!-- 用户密钥管理 -->
        <div class="section">
            <h2>🔑 用户密钥管理</h2>
            <form method="GET">
                <input type="hidden" name="action" value="list_user_keys">
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="number" name="user_id" placeholder="输入用户ID" required>
                    <button type="submit">查询用户密钥</button>
                </div>
            </form>
            
            {% if user_keys %}
            <h3>用户 {{ user_keys_info.username }} (ID: {{ user_keys_info.user_id }}) 的密钥列表:</h3>
            <table>
                <tr>
                    <th>密钥ID</th>
                    <th>密钥</th>
                    <th>状态</th>
                    <th>添加时间</th>
                    <th>操作</th>
                </tr>
                {% for key in user_keys %}
                <tr>
                    <td>{{ key.id }}</td>
                    <td class="key-cell">{{ key.key }}</td>
                    <td>{{ '有效' if key.is_valid else '无效' }}</td>
                    <td>{{ key.created_at }}</td>
                    <td>
                        <form method="POST" style="display: inline;" onsubmit="return confirm('确定要删除这个密钥吗？')">
                            <input type="hidden" name="action" value="delete_key">
                            <input type="hidden" name="key_id" value="{{ key.id }}">
                            <input type="hidden" name="user_id" value="{{ user_keys_info.user_id }}">
                            <button type="submit" class="delete-btn">删除</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </table>
            {% endif %}
        </div>
        
        <!-- 系统统计 -->
        <div class="section">
            <h2>📊 系统统计</h2>
            <form method="GET">
                <input type="hidden" name="action" value="system_stats">
                <button type="submit">获取系统统计</button>
            </form>
            
            {% if stats %}
            <table>
                <tr><th>项目</th><th>数值</th></tr>
                <tr><td>总用户数</td><td>{{ stats.total_users }}</td></tr>
                <tr><td>总密钥数</td><td>{{ stats.total_keys }}</td></tr>
                <tr><td>有效密钥数</td><td>{{ stats.valid_keys }}</td></tr>
                <tr><td>今日总请求数</td><td>{{ stats.today_requests }}</td></tr>
                <tr><td>总请求数</td><td>{{ stats.total_requests }}</td></tr>
            </table>
            {% endif %}
        </div>
    </div>
</body>
</html>
"""


@admin_panel_bp.route('/')
def admin_panel():
    """管理员后台主页面"""
    # 检查管理员权限
    api_key = request.args.get('key')
    if not api_key:
        return "需要提供管理员API密钥: ?key=YOUR_ADMIN_KEY", 401
    
    user = get_user_by_api_key(api_key)
    if not user or user['id'] != 1:  # 假设ID为1的是管理员
        return "需要管理员权限", 403
    
    # 处理各种操作
    action = request.args.get('action')
    message = None
    message_type = 'success'
    
    context = {
        'message': message,
        'message_type': message_type,
        'users': None,
        'search_result': None,
        'user_keys': None,
        'user_keys_info': None,
        'stats': None
    }
    
    try:
        if action == 'list_users':
            context['users'] = get_users_list()
        elif action == 'search_user':
            username = request.args.get('username')
            if username:
                context['search_result'] = search_user_by_username(username)
                if not context['search_result']:
                    context['message'] = f"未找到用户名为 '{username}' 的用户"
                    context['message_type'] = 'error'
        elif action == 'search_user_by_id':
            user_id = request.args.get('user_id')
            if user_id:
                context['search_result'] = search_user_by_id(int(user_id))
                if not context['search_result']:
                    context['message'] = f"未找到ID为 {user_id} 的用户"
                    context['message_type'] = 'error'
        elif action == 'list_user_keys':
            user_id = request.args.get('user_id')
            if user_id:
                user_id = int(user_id)
                user_info = search_user_by_id(user_id)
                if user_info:
                    context['user_keys'] = get_user_keys(user_id)
                    context['user_keys_info'] = {
                        'user_id': user_id,
                        'username': user_info['username'] or f'User_{user_id}'
                    }
                else:
                    context['message'] = f"未找到ID为 {user_id} 的用户"
                    context['message_type'] = 'error'
        elif action == 'system_stats':
            context['stats'] = get_system_stats()
            
    except Exception as e:
        log_error(f"管理员后台操作失败: {e}")
        context['message'] = f"操作失败: {str(e)}"
        context['message_type'] = 'error'
    
    return render_template_string(ADMIN_TEMPLATE, **context)


@admin_panel_bp.route('/', methods=['POST'])
def admin_panel_post():
    """处理POST请求（删除操作等）"""
    # 检查管理员权限
    api_key = request.args.get('key')
    if not api_key:
        return "需要提供管理员API密钥", 401
    
    user = get_user_by_api_key(api_key)
    if not user or user['id'] != 1:
        return "需要管理员权限", 403
    
    action = request.form.get('action')
    
    if action == 'delete_key':
        key_id = request.form.get('key_id')
        user_id = request.form.get('user_id')
        
        try:
            # 删除密钥
            result = execute_db('DELETE FROM api_keys WHERE id = ?', [key_id])
            if result:
                log_info(f"管理员删除了密钥 ID={key_id}")
                # 重定向回密钥列表页面
                return redirect(url_for('admin_panel.admin_panel', 
                                      key=api_key, 
                                      action='list_user_keys', 
                                      user_id=user_id,
                                      message='密钥删除成功'))
            else:
                return redirect(url_for('admin_panel.admin_panel', 
                                      key=api_key,
                                      message='密钥删除失败',
                                      message_type='error'))
        except Exception as e:
            log_error(f"删除密钥失败: {e}")
            return redirect(url_for('admin_panel.admin_panel', 
                                  key=api_key,
                                  message=f'删除失败: {str(e)}',
                                  message_type='error'))
    
    return redirect(url_for('admin_panel.admin_panel', key=api_key))


def get_users_list():
    """获取用户列表"""
    users = query_db("""
        SELECT u.*, COUNT(k.id) as key_count
        FROM users u
        LEFT JOIN api_keys k ON u.id = k.contributor_id
        GROUP BY u.id
        ORDER BY u.id
    """)
    return users


def search_user_by_username(username):
    """根据用户名搜索用户"""
    return query_db('SELECT * FROM users WHERE username = ?', [username], one=True)


def search_user_by_id(user_id):
    """根据用户ID搜索用户"""
    return query_db('SELECT * FROM users WHERE id = ?', [user_id], one=True)


def get_user_keys(user_id):
    """获取用户的密钥列表"""
    return query_db("""
        SELECT id, key, is_valid, created_at
        FROM api_keys 
        WHERE contributor_id = ?
        ORDER BY created_at DESC
    """, [user_id])


def get_system_stats():
    """获取系统统计信息"""
    from datetime import datetime
    
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999).strftime('%Y-%m-%d %H:%M:%S')
    
    stats = {}
    
    # 总用户数
    result = query_db('SELECT COUNT(*) as count FROM users', one=True)
    stats['total_users'] = result['count'] if result else 0
    
    # 总密钥数
    result = query_db('SELECT COUNT(*) as count FROM api_keys', one=True)
    stats['total_keys'] = result['count'] if result else 0
    
    # 有效密钥数
    result = query_db('SELECT COUNT(*) as count FROM api_keys WHERE is_valid = 1', one=True)
    stats['valid_keys'] = result['count'] if result else 0
    
    # 今日请求数
    result = query_db(
        'SELECT COUNT(*) as count FROM usage_logs WHERE request_time BETWEEN ? AND ?',
        [today_start, today_end], one=True
    )
    stats['today_requests'] = result['count'] if result else 0
    
    # 总请求数
    result = query_db('SELECT COUNT(*) as count FROM usage_logs', one=True)
    stats['total_requests'] = result['count'] if result else 0
    
    return stats
