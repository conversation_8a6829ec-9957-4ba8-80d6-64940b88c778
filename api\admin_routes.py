# -*- coding: utf-8 -*-
from flask import Blueprint, request, jsonify
from datetime import datetime
from database import query_db, get_db
from services import get_user_by_api_key
from utils.logger import log_info, log_warning, log_error

admin_bp = Blueprint('admin', __name__, url_prefix='/api/admin')


@admin_bp.route('/usageTotal', methods=['GET', 'POST'])
def admin_usage_total():
    """管理员获取用户使用统计"""
    api_key = request.args.get('key')
    if not api_key:
        return jsonify({"error": "API key required"}), 401
    
    user = get_user_by_api_key(api_key)
    if not user:
        return jsonify({"error": "Invalid API key"}), 401
    if user['id'] != 1:
        return jsonify({"error": "Admin access required"}), 403
    
    is_today = int(request.args.get('today', 0)) == 1
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999).strftime('%Y-%m-%d %H:%M:%S')
    
    if is_today:
        usage = query_db(
            'SELECT user_id, COUNT(*) AS total_usage FROM usage_logs WHERE request_time BETWEEN ? AND ? GROUP BY user_id ',
            [today_start, today_end],
        )
    else:
        usage = query_db(
            'SELECT user_id, COUNT(*) AS total_usage FROM usage_logs GROUP BY user_id ORDER BY total_usage DESC',
        )
    
    return jsonify([{
        "user_id": row['user_id'],
        "total_usage": row['total_usage']
    } for row in usage])


@admin_bp.route('/withdrawKeys', methods=['GET', 'POST'])
def admin_withdraw_keys():
    """管理员撤回用户的密钥"""
    api_key = request.args.get('key')
    if not api_key:
        return jsonify({"error": "API key required"}), 401
    
    user = get_user_by_api_key(api_key) 
    if not user:
        return jsonify({"error": "Invalid API key"}), 401
    if user['id'] != 1:
        return jsonify({"error": "Admin access required"}), 403
    
    user_id_str = request.args.get('user_id')
    if not user_id_str:
        return jsonify({"error": "User ID required"}), 400
    
    try:
        user_id = int(user_id_str)
    except ValueError:
        return jsonify({"error": "Invalid User ID format"}), 400
    
    keys_list = []
    db = get_db()
    cursor = db.cursor()
    
    try:
        # 查询指定 user_id 的所有 key
        cursor.execute("SELECT key FROM api_keys WHERE contributor_id = ?", (user_id,))
        rows = cursor.fetchall()
        keys_list = [row[0] for row in rows]
        
        # 从数据库删除这些行
        if keys_list:
            cursor.execute("DELETE FROM api_keys WHERE contributor_id = ?", (user_id,))
            db.commit()
            log_info(f"管理员撤回了用户 {user_id} 的 {len(keys_list)} 个密钥")
        
        return jsonify({"keys": keys_list})
    except Exception as e:
        log_error(f"撤回用户密钥时出错: {e}")
        db.rollback()
        return jsonify({"error": "Failed to withdraw keys"}), 500
