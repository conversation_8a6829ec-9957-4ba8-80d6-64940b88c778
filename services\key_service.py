# -*- coding: utf-8 -*-
import sqlite3
import requests
from database import query_db, get_db
from utils import ThreadSafeListQueue
from utils.config import GEMINI_API_BASE
from utils.logger import log_debug, log_warning, log_info, log_error


# 全局密钥池
gemini_key_pool = ThreadSafeListQueue()
rate_limited_reached_pool = ThreadSafeListQueue()


def validate_gemini_key(key):
    """通过向 Gemini API 发送简单请求来验证密钥的有效性。"""
    if " " in key:
        return False  # 如果包含空格，直接返回无效

    url = f"{GEMINI_API_BASE}/v1beta/models?key={key}"
    log_debug(f"正在通过 API 请求验证 Gemini 密钥: key=***{key[-4:]}")

    try:
        from utils.resource_manager import safe_http_request
        with safe_http_request('GET', url, timeout=10) as response:
            is_valid = response.status_code == 200
            log_debug(f"密钥 key=***{key[-4:]} 验证 API 响应状态码: {response.status_code} -> {'有效' if is_valid else '无效'}")
            return is_valid
    except requests.exceptions.Timeout:
        log_warning(f"验证密钥 key=***{key[-4:]} 时请求超时。")
        return False
    except requests.exceptions.RequestException as e:
        log_error(f"验证密钥 key=***{key[-4:]} 时发生网络请求错误: {e}")
        return False
    except Exception as e:
        log_error(f"验证密钥 key=***{key[-4:]} 时发生未知错误: {e}")
        return False


def add_gemini_key(key, user_id):
    """添加一个新的 Gemini API 密钥到数据库和运行时池。"""
    key = key.strip()  # 去除首尾空格
    if not key:
        log_warning(f"用户 ID={user_id} 尝试添加空的或只包含空格的 Gemini 密钥。")
        return False
    
    log_info(f"用户 ID={user_id} 尝试添加 Gemini 密钥: key=***{key[-4:]}")
    
    # 首先验证密钥有效性
    if validate_gemini_key(key):
        log_info(f"Gemini 密钥 key=***{key[-4:]} 验证通过。")
        db = get_db()
        try:
            db.execute(
                'INSERT INTO api_keys (key, contributor_id) VALUES (?, ?)',
                [key, user_id]
            )
            db.commit()
            log_info(f"Gemini 密钥 key=***{key[-4:]} 已成功添加到数据库，贡献者 ID={user_id}。")
            
            # 添加到运行时池
            global gemini_key_pool
            if key not in gemini_key_pool:
                gemini_key_pool.append(key)
                log_info(f"Gemini 密钥 key=***{key[-4:]} 已添加到运行时池。当前池大小: {len(gemini_key_pool)}")
            return True
        except sqlite3.IntegrityError:
            # 密钥已存在
            log_warning(f"尝试添加已存在的 Gemini 密钥 key=***{key[-4:]} (贡献者 ID={user_id})。")
            # 即使密钥已存在，如果不在运行时池中，也可能需要添加
            if key not in gemini_key_pool:
                existing_key_info = query_db('SELECT is_valid FROM api_keys WHERE key = ?', [key], one=True)
                if existing_key_info and existing_key_info['is_valid']:
                    gemini_key_pool.append(key)
                    log_info(f"已存在的 Gemini 密钥 key=***{key[-4:]} 被重新添加到运行时池。当前池大小: {len(gemini_key_pool)}")
            return False
    else:
        log_warning(f"用户 ID={user_id} 尝试添加的 Gemini 密钥 key=***{key[-4:]} 未通过验证。")
        return False


def update_gemini_keys():
    """从数据库加载所有有效的 Gemini 密钥到运行时池。"""
    log_info("正在从数据库更新运行时 Gemini 密钥池...")
    global gemini_key_pool

    try:
        from utils.resource_manager import get_db_connection
        with get_db_connection() as db_conn:
            cursor = db_conn.execute('SELECT key FROM api_keys WHERE is_valid = 1')
            new_key_pool = [row[0] for row in cursor.fetchall()]
            gemini_key_pool = ThreadSafeListQueue()
            gemini_key_pool._list = new_key_pool
            log_info(f"运行时 Gemini 密钥池已更新。加载了 {len(gemini_key_pool)} 个有效密钥。")
    except sqlite3.Error as e:
        log_error(f"从数据库更新 Gemini 密钥池时出错: {e}")
    except Exception as e:
        log_error(f"更新密钥池时发生未知错误: {e}")


def set_key_invalid(key):
    """将数据库中该 key 标记为 is_valid=0，并从运行时的 pool 中移除。"""
    log_warning(f"将 Gemini 密钥 key=***{key[-4:]} 标记为无效...")
    db = get_db()
    try:
        db.execute('UPDATE api_keys SET is_valid = 0 WHERE key = ?', [key])
        db.commit()
        log_info(f"数据库中的 Gemini 密钥 key=***{key[-4:]} 已标记为无效。")
        if key in gemini_key_pool:
            gemini_key_pool.remove(key)
            log_info(f"Gemini 密钥 key=***{key[-4:]} 已从运行时池中移除。当前池大小: {len(gemini_key_pool)}")
    except sqlite3.Error as e:
        log_error(f"标记 Gemini 密钥 key=***{key[-4:]} 为无效时出错: {e}")
        db.rollback()


def get_available_gemini_key():
    """从运行时池中随机选择一个可用的 Gemini API 密钥。"""
    if not gemini_key_pool:
        log_error("Gemini 密钥池为空！无法提供密钥。")
        return None
    
    keyCount = len(gemini_key_pool) + len(rate_limited_reached_pool)
    if len(gemini_key_pool) < 0.5 * keyCount:
        log_warning(f"Gemini 密钥池过小 ({len(gemini_key_pool)}/{keyCount}) 从速率限制池取回密钥")
        while not rate_limited_reached_pool.empty():
            gemini_key_pool.put(rate_limited_reached_pool.get())

    selected_key = gemini_key_pool.get()
    gemini_key_pool.put(selected_key)  # 将密钥放回池中
    log_debug(f"从池中随机选择了一个 Gemini 密钥: key=***{selected_key[-4:]} (池大小: {len(gemini_key_pool)})")
    return selected_key


def key_reach_limit(key):
    """将达到速率限制的密钥移到限制池"""
    gemini_key_pool.remove(key)  # 从池中移除密钥
    rate_limited_reached_pool.put(key)  # 将密钥放入速率限制池
    log_warning(f"密钥 key=***{key[-4:]} 达到速率限制 and has been moved to reached rate limit pool")


def invalidate_keys_with_whitespace():
    """检查数据库中所有有效的 Gemini 密钥，如果包含空格，则标记为无效。"""
    log_info("开始检查数据库中有效密钥是否包含空格...")
    invalidated_count = 0
    keys_to_invalidate = []

    try:
        from utils.resource_manager import get_db_connection
        with get_db_connection() as db_conn:
            cursor = db_conn.cursor()
            cursor.execute('SELECT id, key FROM api_keys')
            valid_keys = cursor.fetchall()
            log_debug(f"找到 {len(valid_keys)} 个标记为有效的密钥进行空格检查。")

            for key_id, key_value in valid_keys:
                if key_value is not None and " " in key_value:
                    log_warning(f"密钥 ID={key_id} (key=***{key_value[-4:]}) 包含空格，将被标记为无效。")
                    keys_to_invalidate.append(key_id)
                    invalidated_count += 1

            if keys_to_invalidate:
                update_query = "DELETE FROM api_keys WHERE id = ?"
                params = [(key_id,) for key_id in keys_to_invalidate]
                cursor.executemany(update_query, params)
                db_conn.commit()
                log_info(f"成功将 {invalidated_count} 个包含空格的密钥被删除")
            else:
                log_info("没有发现包含空格的有效密钥。")

    except sqlite3.Error as e:
        log_error(f"检查或标记包含空格的密钥时出错: {e}")
    except Exception as e:
        log_error(f"处理空格密钥检查时发生意外错误: {e}")


def check_all_keys():
    """启动时批量检查数据库中的所有 key 是否可用。"""
    log_info("开始批量检查数据库中所有标记为有效的 Gemini 密钥...")

    try:
        from utils.resource_manager import get_db_connection
        with get_db_connection() as db_conn:
            rows = db_conn.execute('SELECT key FROM api_keys WHERE is_valid = 1').fetchall()
    except sqlite3.Error as e:
        log_error(f"查询有效密钥以进行检查时出错: {e}")
        return
    except Exception as e:
        log_error(f"获取数据库连接进行密钥检查时出错: {e}")
        return

    if not rows:
        log_info("数据库中没有标记为有效的密钥需要检查。")
        return

    invalid_count = 0
    total_checked = len(rows)
    log_info(f"将检查 {total_checked} 个密钥...")

    keys_to_check = [row[0] for row in rows]

    for k in keys_to_check:
        log_debug(f"正在验证密钥: key=***{k[-4:]}")
        if not validate_gemini_key(k):
            log_warning(f"密钥 key=***{k[-4:]} 验证失败，将标记为无效。")
            set_key_invalid(k)
            invalid_count += 1
        else:
            log_debug(f"密钥 key=***{k[-4:]} 验证通过。")

    log_info(f"批量密钥检查完成。总共检查: {total_checked}，发现无效: {invalid_count}。")


def get_total_quota():
    """计算当前的总理论配额。"""
    log_warning("总配额计算基于每个密钥 50 次/天的假设，这可能不准确。Gemini 的实际限制是按分钟计算的。")
    calculated_quota = len(gemini_key_pool) * 50 
    log_debug(f"计算的总理论配额: {len(gemini_key_pool)} 个密钥 * 50 次/密钥/天 = {calculated_quota}")
    return calculated_quota
