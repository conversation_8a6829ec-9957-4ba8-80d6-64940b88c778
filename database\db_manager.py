# -*- coding: utf-8 -*-
import sqlite3
from flask import g
from utils.config import DATABASE
from utils.logger import log_debug, log_error


def get_db():
    """获取当前请求上下文的数据库连接。如果不存在，则创建一个新连接。"""
    db = getattr(g, '_database', None)
    if db is None:
        log_debug(f"数据库连接不存在，正在创建新连接到 {DATABASE}...")
        db = g._database = sqlite3.connect(DATABASE)
        db.row_factory = sqlite3.Row  # 设置行工厂以字典形式访问列
        log_debug("新数据库连接已创建并存储在 g 对象中。")
    return db


def close_connection(exception):
    """在应用上下文结束时关闭数据库连接。"""
    db = getattr(g, '_database', None)
    if db is not None:
        log_debug("应用上下文结束，正在关闭数据库连接...")
        db.close()
        log_debug("数据库连接已关闭。")
    if exception:
        log_error(f"应用上下文销毁时发生异常: {exception}")


def query_db(query, args=(), one=False):
    """执行数据库查询并返回结果。"""
    log_debug(f"执行数据库查询: {query} | 参数: {args}")
    try:
        cur = get_db().execute(query, args)
        rv = cur.fetchall()
        cur.close()
        result = (rv[0] if rv else None) if one else rv
        log_debug(f"查询完成。返回 {'单个' if one else len(rv)} 条记录。")
        return result
    except sqlite3.Error as e:
        log_error(f"数据库查询失败: {query} | 参数: {args} | 错误: {e}")
        return None  # 查询失败时返回 None


def execute_db(query, args=(), commit=True):
    """执行数据库操作（INSERT, UPDATE, DELETE）"""
    log_debug(f"执行数据库操作: {query} | 参数: {args}")
    try:
        db = get_db()
        cursor = db.execute(query, args)
        if commit:
            db.commit()
        log_debug("数据库操作执行成功。")
        return cursor
    except sqlite3.Error as e:
        log_error(f"数据库操作失败: {query} | 参数: {args} | 错误: {e}")
        if commit:
            get_db().rollback()
        return None


def get_direct_db_connection():
    """获取直接的数据库连接（不依赖Flask上下文）"""
    try:
        db = sqlite3.connect(DATABASE)
        db.row_factory = sqlite3.Row
        return db
    except sqlite3.Error as e:
        log_error(f"创建直接数据库连接失败: {e}")
        return None
