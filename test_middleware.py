# -*- coding: utf-8 -*-
"""
测试模型中间件功能
"""
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.model_middleware import process_model_request, get_model_processor_info
from utils.extension_manager import get_extension_manager


def test_model_middleware():
    """测试模型中间件功能"""
    print("=== 测试模型中间件功能 ===\n")
    
    # 测试1: 基础模型（无后缀）
    print("1. 测试基础模型:")
    model_name = "gemini-2.5-pro"
    payload = {"contents": [{"parts": [{"text": "Hello"}]}]}
    
    new_model, new_payload = process_model_request(model_name, payload)
    print(f"   输入模型: {model_name}")
    print(f"   输出模型: {new_model}")
    print(f"   载荷变化: {'是' if new_payload != payload else '否'}")
    print()
    
    # 测试2: 搜索功能
    print("2. 测试搜索功能:")
    model_name = "gemini-2.5-pro#search"
    payload = {"contents": [{"parts": [{"text": "What's the weather today?"}]}]}

    new_model, new_payload = process_model_request(model_name, payload)
    print(f"   输入模型: {model_name}")
    print(f"   输出模型: {new_model}")
    print(f"   载荷变化: {'是' if new_payload != payload else '否'}")
    if 'tools' in new_payload:
        print(f"   添加的工具: {new_payload['tools']}")
    print()

    # 测试3: 多个后缀
    print("3. 测试多个后缀:")
    model_name = "gemini-2.5-flash#search#fast"
    payload = {"contents": [{"parts": [{"text": "Quick search query"}]}]}

    new_model, new_payload = process_model_request(model_name, payload)
    print(f"   输入模型: {model_name}")
    print(f"   输出模型: {new_model}")
    print(f"   载荷变化: {'是' if new_payload != payload else '否'}")
    if 'tools' in new_payload:
        print(f"   添加的工具: {new_payload['tools']}")
    if 'generationConfig' in new_payload:
        print(f"   生成配置: {new_payload['generationConfig']}")
    print()

    # 测试4: 专业模式
    print("4. 测试专业模式:")
    model_name = "gemini-2.5-pro#pro"
    payload = {"contents": [{"parts": [{"text": "Complex analysis task"}]}]}

    new_model, new_payload = process_model_request(model_name, payload)
    print(f"   输入模型: {model_name}")
    print(f"   输出模型: {new_model}")
    print(f"   载荷变化: {'是' if new_payload != payload else '否'}")
    if 'generationConfig' in new_payload:
        print(f"   生成配置: {new_payload['generationConfig']}")
    print()


def test_processor_info():
    """测试处理器信息获取"""
    print("=== 测试处理器信息 ===\n")
    
    processors = get_model_processor_info()
    print("可用的处理器:")
    for suffix, info in processors.items():
        print(f"  -{suffix}: {info['doc']}")
    print()


def test_extension_manager():
    """测试扩展管理器"""
    print("=== 测试扩展管理器 ===\n")
    
    manager = get_extension_manager()
    
    print("已加载的扩展:")
    extensions = manager.list_extensions()
    if extensions:
        for ext_name in extensions:
            ext_info = manager.get_extension_info(ext_name)
            print(f"  {ext_name}: {ext_info['doc']}")
    else:
        print("  无已加载的扩展")
    print()
    
    print("可用的钩子:")
    hooks = manager.list_hooks()
    if hooks:
        for hook_name, count in hooks.items():
            print(f"  {hook_name}: {count} 个回调")
    else:
        print("  无可用钩子")
    print()
    
    print("可用的中间件:")
    middleware = manager.list_middleware()
    if middleware:
        for mw_name in middleware:
            print(f"  {mw_name}")
    else:
        print("  无可用中间件")
    print()


if __name__ == "__main__":
    try:
        test_model_middleware()
        test_processor_info()
        test_extension_manager()
        print("✅ 所有测试完成!")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
