-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    linuxdo_id INTEGER UNIQUE,
    username TEXT,
    email TEXT,
    avatar_url TEXT,
    api_key TEXT UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- API keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE,
    contributor_id INTEGER,
    is_valid BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (contributor_id) REFERENCES users (id)
);

-- Usage logs table
CREATE TABLE IF NOT EXISTS usage_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    endpoint TEXT,
    status_code INTEGER,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users (id)
);
